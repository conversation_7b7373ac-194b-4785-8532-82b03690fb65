import { Metadata } from "next";
import { ComplianceCategoryClientWrapper } from "@/features/compliance";
import { formatProvinceTitle } from "@/features/compliance/lib/api-client";

interface ComplianceCategoryPageProps {
  params: Promise<{ category: string }>;
}

export async function generateMetadata({ params }: ComplianceCategoryPageProps): Promise<Metadata> {
  const { category } = await params;
  const categoryTitle = formatProvinceTitle(category);
  
  return {
    title: `${categoryTitle} Compliance | Indi Central`,
    description: `Compliance documents for ${categoryTitle}`,
  };
}

export default async function ComplianceCategoryPage({ params }: ComplianceCategoryPageProps) {
  const { category } = await params;
  return <ComplianceCategoryClientWrapper category={category} />;
}
