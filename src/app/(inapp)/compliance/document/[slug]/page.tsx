import { Metadata } from "next";
import { ComplianceDocumentClientWrapper } from "@/features/compliance";

interface ComplianceDocumentPageProps {
  params: Promise<{ slug: string }>;
  searchParams: Promise<{ 
    documentId?: string; 
    title?: string;
    file?: string;
    ext?: string;
    filetype?: string;
  }>;
}

export async function generateMetadata({ params, searchParams }: ComplianceDocumentPageProps): Promise<Metadata> {
  const { slug } = await params;
  const searchParamsData = await searchParams;
  const title = searchParamsData.title || slug.replace(/-/g, ' ');
  
  return {
    title: `${title} | Compliance | Indi Central`,
    description: `View and download the compliance document: ${title}`,
  };
}

export default async function ComplianceDocumentPage({ params, searchParams }: ComplianceDocumentPageProps) {
  const { slug } = await params;
  const { documentId, title, file, ext, filetype } = await searchParams;
  
  if (!documentId || documentId === 'undefined') {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-destructive mb-4">Invalid Document</h1>
          <p className="text-muted-foreground">
            No document ID provided. Please navigate to this page from the compliance list.
          </p>
        </div>
      </div>
    );
  }

  return (
    <ComplianceDocumentClientWrapper 
      documentId={documentId}
      title={title}
      file={file}
      ext={ext}
      filetype={filetype}
    />
  );
}
