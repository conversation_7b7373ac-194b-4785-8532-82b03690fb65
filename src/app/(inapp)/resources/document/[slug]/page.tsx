import { Metadata } from "next";
import { ResourcesDocumentClientWrapper } from "@/features/resources";

interface ResourcesDocumentPageProps {
  params: Promise<{ slug: string }>;
  searchParams: Promise<{ 
    documentId?: string; 
    title?: string;
    file?: string;
    ext?: string;
    filetype?: string;
  }>;
}

export async function generateMetadata({ params, searchParams }: ResourcesDocumentPageProps): Promise<Metadata> {
  const { slug } = await params;
  const searchParamsData = await searchParams;
  const title = searchParamsData.title || slug.replace(/-/g, ' ');
  
  return {
    title: `${title} | Resources | Indi Central`,
    description: `View and download the resource document: ${title}`,
  };
}

export default async function ResourcesDocumentPage({ params, searchParams }: ResourcesDocumentPageProps) {
  const { slug } = await params;
  const { documentId, title, file, ext, filetype } = await searchParams;
  
  if (!documentId || documentId === 'undefined') {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-destructive mb-4">Invalid Document</h1>
          <p className="text-muted-foreground">
            No document ID provided. Please navigate to this page from the resources list.
          </p>
        </div>
      </div>
    );
  }

  return (
    <ResourcesDocumentClientWrapper 
      documentId={documentId}
      title={title}
      file={file}
      ext={ext}
      filetype={filetype}
    />
  );
}
