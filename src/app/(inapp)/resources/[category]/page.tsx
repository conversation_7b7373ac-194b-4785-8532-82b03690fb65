import { Metadata } from "next";
import { ResourcesCategoryClientWrapper } from "@/features/resources";
import { formatProvinceTitle } from "@/features/resources/lib/api-client";

interface ResourcesCategoryPageProps {
  params: Promise<{ category: string }>;
}

export async function generateMetadata({ params }: ResourcesCategoryPageProps): Promise<Metadata> {
  const { category } = await params;
  const categoryTitle = formatProvinceTitle(category);
  
  return {
    title: `${categoryTitle} Resources | Indi Central`,
    description: `Resource documents for ${categoryTitle}`,
  };
}

export default async function ResourcesCategoryPage({ params }: ResourcesCategoryPageProps) {
  const { category } = await params;
  return <ResourcesCategoryClientWrapper category={category} />;
}
