import { Metadata } from "next";
import { PrintablesDocumentClientWrapper } from "@/features/printables";

interface PrintablesDocumentPageProps {
  params: Promise<{ slug: string }>;
  searchParams: Promise<{
    documentId?: string;
    title?: string;
    file?: string;
    ext?: string;
    filetype?: string;
    position?: string;
    qrId?: string;
  }>;
}

export async function generateMetadata({ params, searchParams }: PrintablesDocumentPageProps): Promise<Metadata> {
  const { slug } = await params;
  const searchParamsData = await searchParams;
  const title = searchParamsData.title || slug.replace(/-/g, ' ');

  return {
    title: `${title} | Printables | Indi Central`,
    description: `View and download the printable document: ${title}`,
  };
}

export default async function PrintablesDocumentPage({ params, searchParams }: PrintablesDocumentPageProps) {
  const { slug } = await params;
  const { documentId, title, file, ext, filetype, position, qrId } = await searchParams;
  
  if (!documentId || documentId === 'undefined') {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-destructive mb-4">Invalid Document</h1>
          <p className="text-muted-foreground">
            No document ID provided. Please navigate to this page from the printables list.
          </p>
        </div>
      </div>
    );
  }

  return (
    <PrintablesDocumentClientWrapper 
      documentId={documentId}
      title={title}
      file={file}
      ext={ext}
      filetype={filetype}
      position={position}
      qrId={qrId}
    />
  );
}
