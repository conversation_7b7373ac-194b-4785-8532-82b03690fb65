"use server";

import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { env } from "@/shared/lib/env";

export interface LoginResult {
  success: boolean;
  error?: string;
}

/**
 * Server action for user login
 * This runs on the server and can securely handle authentication
 */
export async function loginAction(
  identifier: string,
  password: string
): Promise<LoginResult> {
  try {
    // Updated for Strapi V5: /auth/local -> /api/auth/local
    const API_URL = `${env.API_URL}/auth/local`;
    const loginInfo = { identifier, password };

    console.log("Server login attempt to:", API_URL);

    const response = await fetch(API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(loginInfo),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error("Server login error:", response.status, errorData);

      // Parse error message if possible
      try {
        const parsedError = JSON.parse(errorData);
        const errorMessage =
          parsedError.message?.[0]?.messages?.[0]?.message ||
          parsedError.error?.message ||
          "Invalid credentials";
        return { success: false, error: errorMessage };
      } catch {
        return {
          success: false,
          error: "Login failed. Please check your credentials.",
        };
      }
    }

    const data = await response.json();
    const { jwt, cookie_token, user } = data;

    if (!jwt || !user) {
      console.error("Invalid login response:", data);
      return { success: false, error: "Invalid response from server" };
    }

    // Set secure HTTP-only cookies on the server
    const cookieStore = await cookies();

    // Set JWT cookie with security flags
    cookieStore.set("jwt", jwt, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: 30 * 24 * 60 * 60, // 30 days
      path: "/",
    });

    // Set user ID cookie
    cookieStore.set("userId", String(user.id || user._id), {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: 30 * 24 * 60 * 60, // 30 days
      path: "/",
    });

    // Set Cloudflare token
    cookieStore.set("__cld_token__", cookie_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: 30 * 24 * 60 * 60, // 30 days
      path: "/",
    });

    console.log("Server login successful for user:", user.email);
    return { success: true };
  } catch (error) {
    console.error("Server login error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "An error occurred during login",
    };
  }
}

/**
 * Server action for logout
 */
export async function logoutAction(): Promise<void> {
  const cookieStore = await cookies();

  // Clear all auth-related cookies
  cookieStore.delete("jwt");
  cookieStore.delete("userId");
  cookieStore.delete("__cld_token__");

  // Redirect to login page
  redirect("/login");
}

/**
 * Get user data from server-side
 */
export async function getUserDataAction() {
  try {
    const cookieStore = await cookies();
    const jwt = cookieStore.get("jwt")?.value;
    const userId = cookieStore.get("userId")?.value;

    if (!jwt || !userId) {
      return { userInfo: null, notifications: [], onePages: [] };
    }

    const headers = {
      Authorization: `Bearer ${jwt}`,
      "Content-Type": "application/json",
    };

    // Updated for Strapi V5: All endpoints now require /api/ prefix
    const [userResponse, notificationsResponse, onePagesResponse] =
      await Promise.all([
        fetch(`${env.API_URL}/users-permissions/users-light/${userId}`, {
          headers,
        }),
        fetch(`${env.API_URL}/notifications`, { headers }),
        fetch(`${env.API_URL}/one-pages`, { headers }),
      ]);

    // Handle responses
    const userInfo = userResponse.ok ? await userResponse.json() : null;
    const notifications = notificationsResponse.ok
      ? await notificationsResponse.json()
      : [];
    const onePages = onePagesResponse.ok ? await onePagesResponse.json() : [];

    // Handle both nested and direct response formats for userInfo
    const normalizedUserInfo = userInfo?.data || userInfo;

    return {
      userInfo: normalizedUserInfo,
      notifications: Array.isArray(notifications) ? notifications : [],
      onePages: Array.isArray(onePages) ? onePages : [],
    };
  } catch (error) {
    console.error("Error fetching user data from server:", error);
    return { userInfo: null, notifications: [], onePages: [] };
  }
}
