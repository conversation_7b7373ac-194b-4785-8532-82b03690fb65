import { CategoryItem } from "@/shared/components/category-landing-page";

export interface ApiResponse<T> {
  data: T[];
  meta?: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

export interface DocumentItem {
  id: string;
  title: string;
  description?: string;
  thumbnail?: {
    url: string;
    alternativeText?: string;
    formats?: {
      thumbnail?: { url: string };
    };
  };
  file?: {
    url: string;
    ext: string;
    size?: number;
  };
  provinceFile?: Array<{
    province: string;
    file: {
      url: string;
      ext: string;
      size?: number;
    };
    thumbnail?: {
      url: string;
      alternativeText?: string;
      formats?: {
        thumbnail?: { url: string };
      };
    };
  }>;
  isHidden?: boolean;
  metadata?: Record<string, any>;
}

export interface MediaItem {
  id: string;
  title: string;
  description?: string;
  thumbnail?: {
    url: string;
    alternativeText?: string;
    formats?: {
      thumbnail?: { url: string };
    };
  };
  category?: string;
  tags?: string[];
  mediaItems?: MediaItem[];
  metadata?: Record<string, any>;
}

export interface CategoryApiClientConfig {
  baseUrl: string;
  authToken?: string;
  timeout?: number;
}

export class CategoryApiClient {
  private config: CategoryApiClientConfig;

  constructor(config: CategoryApiClientConfig) {
    this.config = config;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.config.baseUrl}${endpoint}`;
    const headers: HeadersInit = {
      "Content-Type": "application/json",
      ...options.headers,
    };

    if (this.config.authToken) {
      headers.Authorization = `Bearer ${this.config.authToken}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
      signal: AbortSignal.timeout(this.config.timeout || 10000),
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  // Generic method to fetch documents by type
  async fetchDocumentsByType(
    documentType: string,
    options?: {
      category?: string;
      province?: string;
      search?: string;
      limit?: number;
      offset?: number;
    }
  ): Promise<ApiResponse<DocumentItem>> {
    const params = new URLSearchParams();
    params.append("documentType", documentType);
    
    if (options?.category) params.append("category", options.category);
    if (options?.province) params.append("province", options.province);
    if (options?.search) params.append("search", options.search);
    if (options?.limit) params.append("limit", options.limit.toString());
    if (options?.offset) params.append("offset", options.offset.toString());

    return this.request<ApiResponse<DocumentItem>>(`/docs?${params.toString()}`);
  }

  // Fetch printables
  async fetchPrintables(options?: {
    province?: string;
    search?: string;
  }): Promise<ApiResponse<DocumentItem>> {
    return this.fetchDocumentsByType("printable", options);
  }

  // Fetch compliance documents
  async fetchComplianceDocuments(options?: {
    province?: string;
    search?: string;
  }): Promise<ApiResponse<DocumentItem>> {
    return this.fetchDocumentsByType("compliance", options);
  }

  // Fetch resource documents
  async fetchResourceDocuments(options?: {
    province?: string;
    search?: string;
  }): Promise<ApiResponse<DocumentItem>> {
    return this.fetchDocumentsByType("resource", options);
  }

  // Fetch brand materials
  async fetchBrandMaterials(options?: {
    slug?: string;
    category?: string;
    search?: string;
  }): Promise<ApiResponse<MediaItem>> {
    const params = new URLSearchParams();
    
    if (options?.slug) params.append("slug_eq", options.slug);
    if (options?.category) params.append("category", options.category);
    if (options?.search) params.append("search", options.search);

    return this.request<ApiResponse<MediaItem>>(`/brand-materials?${params.toString()}`);
  }

  // Fetch custom shop items
  async fetchCustomShopItems(options?: {
    category?: string;
    search?: string;
  }): Promise<ApiResponse<MediaItem>> {
    const params = new URLSearchParams();
    
    if (options?.category) params.append("category", options.category);
    if (options?.search) params.append("search", options.search);

    return this.request<ApiResponse<MediaItem>>(`/custom-shop?${params.toString()}`);
  }

  // Transform document items to category items for landing pages
  transformDocumentsToCategories(
    documents: DocumentItem[],
    categoryType: "province" | "custom"
  ): CategoryItem[] {
    if (categoryType === "province") {
      // For province-based categories (printables, compliance, resources)
      const provinceMap = new Map<string, DocumentItem[]>();
      
      documents.forEach((doc) => {
        if (doc.provinceFile) {
          doc.provinceFile.forEach((provinceFile) => {
            const province = provinceFile.province;
            if (!provinceMap.has(province)) {
              provinceMap.set(province, []);
            }
            provinceMap.get(province)!.push(doc);
          });
        }
      });

      return Array.from(provinceMap.entries()).map(([province, docs]) => ({
        id: province,
        title: province,
        description: `${docs.length} document${docs.length !== 1 ? "s" : ""} available`,
        metadata: { province, documentCount: docs.length },
      }));
    } else {
      // For custom categories (brand-materials, custom-shop)
      const categoryMap = new Map<string, DocumentItem[]>();
      
      documents.forEach((doc) => {
        const category = doc.metadata?.category || "General";
        if (!categoryMap.has(category)) {
          categoryMap.set(category, []);
        }
        categoryMap.get(category)!.push(doc);
      });

      return Array.from(categoryMap.entries()).map(([category, docs]) => ({
        id: category,
        title: category,
        description: `${docs.length} item${docs.length !== 1 ? "s" : ""} available`,
        metadata: { category, itemCount: docs.length },
      }));
    }
  }

  // Transform media items to category items
  transformMediaToCategories(mediaItems: MediaItem[]): CategoryItem[] {
    const categoryMap = new Map<string, MediaItem[]>();
    
    mediaItems.forEach((item) => {
      const category = item.category || "General";
      if (!categoryMap.has(category)) {
        categoryMap.set(category, []);
      }
      categoryMap.get(category)!.push(item);
    });

    return Array.from(categoryMap.entries()).map(([category, items]) => ({
      id: category,
      title: category,
      description: `${items.length} item${items.length !== 1 ? "s" : ""} available`,
      metadata: { category, itemCount: items.length },
    }));
  }

  // Get items for a specific category
  getItemsForCategory(
    items: (DocumentItem | MediaItem)[],
    category: string,
    categoryType: "province" | "media"
  ): (DocumentItem | MediaItem)[] {
    if (categoryType === "province") {
      return items.filter((item) => {
        if ("provinceFile" in item && item.provinceFile) {
          return item.provinceFile.some((pf) => pf.province === category);
        }
        return false;
      });
    } else {
      return items.filter((item) => {
        if ("category" in item) {
          return item.category === category;
        }
        return false;
      });
    }
  }
}

// Factory function to create API client instances
export function createCategoryApiClient(config: CategoryApiClientConfig): CategoryApiClient {
  return new CategoryApiClient(config);
}

// Default export
export default CategoryApiClient;
