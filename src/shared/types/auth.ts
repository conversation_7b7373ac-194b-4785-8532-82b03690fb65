export interface User {
  id: string;
  documentId?: string;
  name: string;
  email: string;
  avatar?: string;
  role: "admin" | "editor" | "user" | string;
  company?: string;
  firstname?: string;
  lastname?: string;
  username?: string;
  photo?: {
    url?: string;
    [key: string]: any;
  };
  team?: {
    name?: string;
    id?: string;
    logo?: {
      url?: string;
      [key: string]: any;
    };
    showFSRA?: boolean;
    [key: string]: any;
  };
  // Additional fields required by the PDF generator
  position?: string;
  workEmail?: string;
  workPhone?: string;
  phone?: string;
  cellPhone?: string;
  website?: string;
  brokerage?: string;
  bio?: string;
  isOnboarding?: boolean;
  isStaffMember?: boolean;
  notListed?: boolean;
  profileComplete?: boolean;
  websiteOptIn?: boolean;
  createdAt?: string;
  updatedAt?: string;
  province?: string;

  // Printable preferences
  photoOnPrintable?: boolean;
  qrCodeOnPrintable?: boolean;
  emptyPrintableFooter?: boolean;
}

// Raw API response user structure
export interface RawApiUser {
  id?: string;
  _id?: any;
  documentId?: string;
  firstname?: string;
  lastname?: string;
  username?: string;
  name?: string;
  email?: string;
  role?: string | { name?: string; [key: string]: any };
  avatar?: string;
  photo?: {
    url?: string;
    [key: string]: any;
  };
  team?: {
    name?: string;
    id?: string;
    logo?: {
      url?: string;
      [key: string]: any;
    };
    showFSRA?: boolean;
    [key: string]: any;
  };
  company?: string;
  // Additional fields that might come from the API
  position?: string;
  workEmail?: string;
  workPhone?: string;
  phone?: string;
  cellPhone?: string;
  website?: string;
  brokerage?: string;
  [key: string]: any; // Allow for additional unknown properties
}

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: "info" | "warning" | "error" | "success";
  read: boolean;
  createdAt: string;
}

export interface OnePage {
  id: string;
  _id?: string;
  Title: string;
  slug: string;
}

export interface AuthState {
  isAuth: boolean;
  userInfo: User | null;
  notifications: Notification[];
  onePages: OnePage[];
  initialized: boolean;
  error: string | null;
}

export interface AuthContextType {
  userAuth: AuthState;
  setUserAuth: React.Dispatch<React.SetStateAction<AuthState>>;
  login: (identifier: string, password: string) => Promise<boolean>;
  logout: () => void;
  refreshUserData: () => Promise<void>;
}

export interface LoginResponse {
  jwt: string;
  documentId: string; 
  cookie_token: string;
  user: User;
}
