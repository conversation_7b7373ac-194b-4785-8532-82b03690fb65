"use client";

import React, { useState, useEffect } from 'react';
import { Alert, AlertDescription } from '@/shared/ui/alert';
import { Button } from '@/shared/ui/button';
import { SocialMediaList } from './social-media-list';
import SocialPostsList from './social-posts-list';
import { useSocialMedia } from '../hooks/use-social-media';
import { useSocialMediaContent } from '../hooks/use-social-media-content';
import { sortPostsByMonth } from '../lib/utils';
import type { LightweightPost } from '../types';

export const SocialMediaPage: React.FC = () => {
  const [showExtras, setShowExtras] = useState(false);
  const [isMonthsMobOpen, setIsMonthsMobOpen] = useState(false);
  
  const { monthlyPosts, extraPosts, isLoading, error, refetch } = useSocialMedia();
  const { content, isLoading: isContentLoading, error: contentError, setContent, contentPageRef } = useSocialMediaContent();
  
  console.log('SocialMediaPage - monthlyPosts:', monthlyPosts?.length);
  console.log('SocialMediaPage - extraPosts:', extraPosts?.length);
  console.log('SocialMediaPage - isLoading:', isLoading);
  console.log('SocialMediaPage - error:', error);

  // Auto-load latest monthly post when component mounts
  useEffect(() => {
    if (monthlyPosts.length > 0 && !content) {
      const latestPost = sortPostsByMonth(monthlyPosts)[0];
      if (latestPost) {
        setContent(latestPost, false);
      }
    }
  }, [monthlyPosts, content]); // Removed setContent from dependencies

  const handlePostSelect = async (post: LightweightPost, isExtra = false) => {
    await setContent(post, isExtra);
  };

  const handleToggleExtras = () => {
    setShowExtras(prev => !prev);
  };

  const handleToggleMonthsMob = () => {
    setIsMonthsMobOpen(prev => !prev);
  };

  const handleRetry = () => {
    refetch();
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="space-y-4">
          <div className="h-8 w-64 bg-muted animate-pulse rounded" />
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="md:col-span-1 space-y-4">
              <div className="h-10 w-full bg-muted animate-pulse rounded" />
              <div className="space-y-2">
                {Array.from({ length: 5 }).map((_, index) => (
                  <div key={index} className="h-12 w-full bg-muted animate-pulse rounded" />
                ))}
              </div>
            </div>
            <div className="md:col-span-3 space-y-4">
              <div className="h-8 w-48 bg-muted animate-pulse rounded" />
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {Array.from({ length: 6 }).map((_, index) => (
                  <div key={index} className="aspect-square w-full bg-muted animate-pulse rounded" />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <Alert className="max-w-md">
          <svg
            className="h-4 w-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
          <AlertDescription className="ml-2">{error}</AlertDescription>
        </Alert>
        <Button onClick={handleRetry} className="mt-4">
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Social media content</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6" ref={contentPageRef}>
        {/* Sidebar */}
        <div className="md:col-span-1">
          <SocialMediaList
            monthlyPosts={monthlyPosts}
            extraPosts={extraPosts}
            onPostSelect={handlePostSelect}
            showExtras={showExtras}
            onToggleExtras={handleToggleExtras}
            isMonthsMobOpen={isMonthsMobOpen}
            onToggleMonthsMob={handleToggleMonthsMob}
            selectedMonth={content?.month}
          />
        </div>

        {/* Content Area */}
        <div className="md:col-span-3">
          {contentError && (
            <Alert className="mb-6">
              <svg
                className="h-4 w-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
              <AlertDescription className="ml-2">{contentError}</AlertDescription>
            </Alert>
          )}

          {isContentLoading && (
            <div className="space-y-4">
              <div className="h-8 w-48 bg-muted animate-pulse rounded" />
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {Array.from({ length: 6 }).map((_, index) => (
                  <div key={index} className="aspect-square w-full bg-muted animate-pulse rounded" />
                ))}
              </div>
            </div>
          )}

          {content && !isContentLoading && (
            <SocialPostsList
              images={content.images}
              captions={content.captions}
              calendar={content.calendar}
              month={content.month}
              isExtra={content.isExtra}
            />
          )}

          {!content && !isContentLoading && !contentError && (
            <div className="text-center py-12">
              <p className="text-muted-foreground">
                Select a month from the sidebar to view content.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
