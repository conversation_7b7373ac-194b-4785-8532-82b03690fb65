// Core social media types
export interface SocialMediaPost {
  id: number;
  documentId: string;
  month: string | null; // Can be null for extra posts
  isExtra: boolean | null;
  extraTitle?: string;
  captionTexts?: CaptionData[];
  calendar?: {
    url: string;
  };
  Province: ProvinceData[];
  createdAt: string;
  updatedAt: string;
}

export interface ProvinceData {
  ref?: ProvinceRef;
  images?: ImageData[] | any[]; // Can be ImageData[] or new backend structure
  all?: boolean;
  [key: string]: any; // For dynamic province properties
}

export interface ProvinceRef {
  all?: boolean;
  images?: ImageData[];
  [key: string]: any; // For dynamic province properties
}

export interface ImageData {
  id: string;
  name: string;
  url: string;
  alternativeText?: string;
  caption?: string;
  width?: number;
  height?: number;
  formats?: {
    thumbnail?: ImageFormat;
    small?: ImageFormat;
    medium?: ImageFormat;
    large?: ImageFormat;
  };
  hash: string;
  ext: string;
  mime: string;
  size: number;
  provider: string;
  createdAt: string;
  updatedAt: string;
}

export interface ImageFormat {
  ext: string;
  url: string;
  hash: string;
  mime: string;
  name: string;
  path: string | null;
  size: number;
  width: number;
  height: number;
  folder: string | null;
  provider: string;
  folderPath: string;
  provider_metadata: unknown;
}

export interface CaptionData {
  id: string;
  title: string;
  postDate?: string;
  text: string;
  backgroundInfo?: string;
}

// Lightweight post for sidebar
export interface LightweightPost {
  id: number;
  documentId: string;
  month: string | null; // Can be null for extra posts
  isExtra: boolean | null;
  extraTitle?: string;
  Province: ProvinceData[];
}

// API Response types
export interface SocialMediaResponse {
  data: SocialMediaPost[];
  meta?: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

export interface SocialMediaPostResponse {
  data: SocialMediaPost;
}

export interface LightweightPostsResponse {
  data: LightweightPost[];
}

// Hook return types
export interface UseSocialMediaResult {
  monthlyPosts: LightweightPost[];
  extraPosts: LightweightPost[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export interface UseSocialMediaContentResult {
  content: SocialMediaContent | null;
  isLoading: boolean;
  error: string | null;
  setContent: (post: LightweightPost, isExtra?: boolean) => Promise<void>;
  contentPageRef: React.RefObject<HTMLDivElement>;
}

export interface SocialMediaContent {
  images: ImageData[];
  captions: CaptionData[] | null;
  calendar: string | null;
  month: string | null; // Can be null for extra posts
  isExtra: boolean;
}

// Component prop types
export interface SocialMediaListProps {
  monthlyPosts: LightweightPost[];
  extraPosts: LightweightPost[];
  onPostSelect: (post: LightweightPost, isExtra?: boolean) => Promise<void>;
  showExtras: boolean;
  onToggleExtras: () => void;
  isMonthsMobOpen: boolean;
  onToggleMonthsMob: () => void;
  selectedMonth?: string; // Add selected month for highlighting
}

export interface SocialPostsListProps {
  images: ImageData[];
  captions: CaptionData[] | null;
  calendar: string | null;
  month: string | null; // Can be null for extra posts
  isExtra: boolean;
}

export interface MonthSelectorProps {
  posts: LightweightPost[];
  onPostSelect: (post: LightweightPost, isExtra?: boolean) => Promise<void>;
  isExtra?: boolean;
  showExtras?: boolean;
  onToggleExtras?: () => void;
}

// Error types
export interface SocialMediaError {
  message: string;
  code?: string;
  field?: string;
}

// Filter types
export interface SocialMediaFilters {
  month?: string;
  _sort?: string;
  _limit?: number;
}
