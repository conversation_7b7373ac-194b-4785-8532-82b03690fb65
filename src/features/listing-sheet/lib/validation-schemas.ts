import { z } from "zod";
import { VALIDATION_CONSTANTS, FILE_UPLOAD_CONSTANTS } from "./constants";
import type { PaymentFrequency, HouseType } from "../types";

// Custom validation helpers
const phoneRegex = new RegExp(VALIDATION_CONSTANTS.PHONE_PATTERN);
const emailRegex = new RegExp(VALIDATION_CONSTANTS.EMAIL_PATTERN);
const mlsCodeRegex = new RegExp(VALIDATION_CONSTANTS.MLS_CODE_PATTERN);

// File upload validation
export const fileUploadSchema = z.object({
  url: z.string().url("Invalid file URL"),
  ext: z.string().optional(),
  formats: z
    .object({
      thumbnail: z.object({ url: z.string().url() }).optional(),
      squared: z.object({ url: z.string().url() }).optional(),
      medium: z.object({ url: z.string().url() }).optional(),
    })
    .optional(),
});

// Property details validation
export const propertyDetailsSchema = z.object({
  askingPrice: z
    .number()
    .min(
      VALIDATION_CONSTANTS.MIN_ASKING_PRICE,
      `Asking price must be at least $${VALIDATION_CONSTANTS.MIN_ASKING_PRICE.toLocaleString()}`
    )
    .max(
      VALIDATION_CONSTANTS.MAX_ASKING_PRICE,
      `Asking price cannot exceed $${VALIDATION_CONSTANTS.MAX_ASKING_PRICE.toLocaleString()}`
    ),

  mlsCode: z
    .string()
    .optional()
    .refine(
      (val) => !val || mlsCodeRegex.test(val),
      "MLS code can only contain letters, numbers, spaces, and hyphens"
    ),

  address: z
    .string()
    .optional()
    .refine(
      (val) => !val || val.trim().length >= 5,
      "Address must be at least 5 characters long"
    ),

  propertyPhoto: fileUploadSchema.optional(),
});

// Realtor information validation
export const realtorInfoSchema = z.object({
  firstname: z
    .string()
    .min(1, "First name is required")
    .max(50, "First name cannot exceed 50 characters"),

  middlename: z
    .string()
    .max(50, "Middle name cannot exceed 50 characters")
    .optional(),

  lastname: z
    .string()
    .min(1, "Last name is required")
    .max(50, "Last name cannot exceed 50 characters"),

  position: z
    .string()
    .min(1, "Position is required")
    .max(100, "Position cannot exceed 100 characters"),

  company: z
    .string()
    .min(1, "Company is required")
    .max(100, "Company cannot exceed 100 characters"),

  email: z
    .string()
    .min(1, "Email is required")
    .regex(emailRegex, "Please enter a valid email address"),

  phone: z
    .string()
    .min(1, "Phone number is required")
    .regex(phoneRegex, "Please enter a valid phone number"),

  website: z
    .string()
    .url("Please enter a valid website URL")
    .optional()
    .or(z.literal("")),

  photo: fileUploadSchema.optional(),
});

// Mortgage calculator validation
export const mortgageCalculatorSchema = z.object({
  years: z
    .number()
    .min(
      VALIDATION_CONSTANTS.MIN_AMORTIZATION_YEARS,
      `Amortization period must be at least ${VALIDATION_CONSTANTS.MIN_AMORTIZATION_YEARS} years`
    )
    .max(
      VALIDATION_CONSTANTS.MAX_AMORTIZATION_YEARS,
      `Amortization period cannot exceed ${VALIDATION_CONSTANTS.MAX_AMORTIZATION_YEARS} years`
    ),

  customYears: z
    .number()
    .min(VALIDATION_CONSTANTS.MIN_AMORTIZATION_YEARS)
    .max(VALIDATION_CONSTANTS.MAX_AMORTIZATION_YEARS)
    .optional(),

  frequency: z.enum([
    "monthly",
    "biweekly",
    "weekly",
    "accbiweekly",
    "accweekly",
  ] as const),

  customPercentage: z
    .number()
    .min(
      VALIDATION_CONSTANTS.MIN_CUSTOM_PERCENTAGE,
      `Custom down payment must be at least ${VALIDATION_CONSTANTS.MIN_CUSTOM_PERCENTAGE}%`
    )
    .max(
      VALIDATION_CONSTANTS.MAX_CUSTOM_PERCENTAGE,
      `Custom down payment cannot exceed ${VALIDATION_CONSTANTS.MAX_CUSTOM_PERCENTAGE}%`
    ),

  rate5: z
    .number()
    .min(
      VALIDATION_CONSTANTS.MIN_INTEREST_RATE,
      `Interest rate must be at least ${VALIDATION_CONSTANTS.MIN_INTEREST_RATE}%`
    )
    .max(
      VALIDATION_CONSTANTS.MAX_INTEREST_RATE,
      `Interest rate cannot exceed ${VALIDATION_CONSTANTS.MAX_INTEREST_RATE}%`
    ),

  rate10: z
    .number()
    .min(VALIDATION_CONSTANTS.MIN_INTEREST_RATE)
    .max(VALIDATION_CONSTANTS.MAX_INTEREST_RATE),

  rate15: z
    .number()
    .min(VALIDATION_CONSTANTS.MIN_INTEREST_RATE)
    .max(VALIDATION_CONSTANTS.MAX_INTEREST_RATE),

  rate20: z
    .number()
    .min(VALIDATION_CONSTANTS.MIN_INTEREST_RATE)
    .max(VALIDATION_CONSTANTS.MAX_INTEREST_RATE),

  rateCustom: z
    .number()
    .min(VALIDATION_CONSTANTS.MIN_INTEREST_RATE)
    .max(VALIDATION_CONSTANTS.MAX_INTEREST_RATE),

  rateFTHB: z
    .number()
    .min(VALIDATION_CONSTANTS.MIN_INTEREST_RATE)
    .max(VALIDATION_CONSTANTS.MAX_INTEREST_RATE),
});

// Expense calculation validation
export const expenseCalculationSchema = z.object({
  // Monthly expenses
  propertyTax: z.number().min(0, "Property tax cannot be negative"),
  propertyInsurance: z.number().min(0, "Property insurance cannot be negative"),
  utilities: z.number().min(0, "Utilities cannot be negative"),
  condoFees: z.number().min(0, "Condo fees cannot be negative"),
  hoaFees: z.number().min(0, "HOA fees cannot be negative"),
  monthlyDebtPayments: z
    .number()
    .min(0, "Monthly debt payments cannot be negative"),
  phone: z.number().min(0, "Phone expenses cannot be negative"),
  cable: z.number().min(0, "Cable expenses cannot be negative"),
  internet: z.number().min(0, "Internet expenses cannot be negative"),

  // Closing costs
  lawyerFee: z.number().min(0, "Lawyer fees cannot be negative"),
  homeInspection: z.number().min(0, "Home inspection cost cannot be negative"),
  appraisal: z.number().min(0, "Appraisal cost cannot be negative"),
  titleInsurance: z.number().min(0, "Title insurance cannot be negative"),
  estoppelFee: z.number().min(0, "Estoppel fee cannot be negative"),
});

// PDF configuration validation
export const pdfConfigurationSchema = z.object({
  full: z.boolean(),
  short: z.boolean(),
  mlsCode: z.string().optional(),
  address: z.string().optional(),
  monthlyExpenses: z.boolean(),
  cashNeeded: z.boolean(),
  propertyPhoto: fileUploadSchema.optional(),
  realtorPhoto: fileUploadSchema.optional(),
});

// Complete listing sheet validation
export const listingSheetSchema = z.object({
  // Property details
  askingPrice: propertyDetailsSchema.shape.askingPrice,
  mlsCode: propertyDetailsSchema.shape.mlsCode,
  address: propertyDetailsSchema.shape.address,
  propertyPhoto: propertyDetailsSchema.shape.propertyPhoto,

  // Mortgage parameters
  years: mortgageCalculatorSchema.shape.years,
  customYears: mortgageCalculatorSchema.shape.customYears,
  frequency: mortgageCalculatorSchema.shape.frequency,
  customPercentage: mortgageCalculatorSchema.shape.customPercentage,

  // Interest rates
  rate5: mortgageCalculatorSchema.shape.rate5,
  rate10: mortgageCalculatorSchema.shape.rate10,
  rate15: mortgageCalculatorSchema.shape.rate15,
  rate20: mortgageCalculatorSchema.shape.rate20,
  rateCustom: mortgageCalculatorSchema.shape.rateCustom,
  rateFTHB: mortgageCalculatorSchema.shape.rateFTHB,

  // Expenses (optional)
  propertyTax: expenseCalculationSchema.shape.propertyTax.optional(),
  propertyInsurance:
    expenseCalculationSchema.shape.propertyInsurance.optional(),
  utilities: expenseCalculationSchema.shape.utilities.optional(),
  condoFees: expenseCalculationSchema.shape.condoFees.optional(),
  hoaFees: expenseCalculationSchema.shape.hoaFees.optional(),
  monthlyDebtPayments:
    expenseCalculationSchema.shape.monthlyDebtPayments.optional(),
  phone: expenseCalculationSchema.shape.phone.optional(),
  cable: expenseCalculationSchema.shape.cable.optional(),
  internet: expenseCalculationSchema.shape.internet.optional(),
  lawyerFee: expenseCalculationSchema.shape.lawyerFee.optional(),
  homeInspection: expenseCalculationSchema.shape.homeInspection.optional(),
  appraisal: expenseCalculationSchema.shape.appraisal.optional(),
  titleInsurance: expenseCalculationSchema.shape.titleInsurance.optional(),
  estoppelFee: expenseCalculationSchema.shape.estoppelFee.optional(),

  // Realtor information
  realtor: realtorInfoSchema,

  // PDF configuration
  pdf: pdfConfigurationSchema,
});

// Form-specific schemas for step-by-step validation
export const propertyFormSchema = z.object({
  askingPrice: z.number().min(1, "Asking price is required"),
  mlsCode: z.string().optional(),
  address: z.string().optional(),
});

export const mortgageFormSchema = z.object({
  years: z.number().min(1, "Amortization period is required"),
  frequency: z.string().min(1, "Payment frequency is required"),
  rate5: z.number().min(0.1, "5% down payment rate is required"),
  rate10: z.number().min(0.1, "10% down payment rate is required"),
  rate15: z.number().min(0.1, "15% down payment rate is required"),
  rate20: z.number().min(0.1, "20% down payment rate is required"),
});

// File validation for uploads
export const validateFileUpload = (
  file: File
): { isValid: boolean; error?: string } => {
  // Check file size
  if (file.size > FILE_UPLOAD_CONSTANTS.MAX_FILE_SIZE) {
    return {
      isValid: false,
      error: `File size must be less than ${
        FILE_UPLOAD_CONSTANTS.MAX_FILE_SIZE / (1024 * 1024)
      }MB`,
    };
  }

  // Check file type
  if (!FILE_UPLOAD_CONSTANTS.ACCEPTED_IMAGE_TYPES.includes(file.type)) {
    return {
      isValid: false,
      error: "Only JPEG and PNG files are allowed",
    };
  }

  return { isValid: true };
};

// Custom validation functions
export const validateCustomDownPayment = (
  customPercentage: number,
  propertyPrice: number
): { isValid: boolean; error?: string; minRequired?: number } => {
  if (propertyPrice <= 0) {
    return { isValid: false, error: "Property price is required" };
  }

  // Determine minimum required based on property price
  let minRequired = 5;

  if (propertyPrice >= 1500000) {
    minRequired = 20;
  } else if (propertyPrice > 500000) {
    // Calculate blended minimum
    const standardPortion = 500000 * 0.05;
    const excessPortion = (propertyPrice - 500000) * 0.1;
    const totalMinimum = standardPortion + excessPortion;
    minRequired = (totalMinimum / propertyPrice) * 100;
  }

  if (customPercentage < minRequired) {
    return {
      isValid: false,
      error: `Minimum down payment required is ${minRequired.toFixed(2)}%`,
      minRequired,
    };
  }

  return { isValid: true, minRequired };
};

export const validateAmortizationForPropertyType = (
  years: number,
  houseType: HouseType
): { isValid: boolean; error?: string; maxYears?: number } => {
  let maxYears = 30;

  if (houseType === "preOwned") {
    maxYears = 25;
  }

  if (years > maxYears) {
    return {
      isValid: false,
      error: `Maximum amortization for ${houseType} properties is ${maxYears} years`,
      maxYears,
    };
  }

  return { isValid: true, maxYears };
};

// Error message utilities
export const getValidationErrorMessage = (error: z.ZodError): string => {
  const firstError = error.errors[0];
  return firstError?.message || "Validation error occurred";
};

export const getFieldErrors = (error: z.ZodError): Record<string, string> => {
  const fieldErrors: Record<string, string> = {};

  error.errors.forEach((err) => {
    const path = err.path.join(".");
    if (path) {
      fieldErrors[path] = err.message;
    }
  });

  return fieldErrors;
};

// Type exports for form validation
export type PropertyDetailsFormData = z.infer<typeof propertyDetailsSchema>;
export type RealtorInfoFormData = z.infer<typeof realtorInfoSchema>;
export type MortgageCalculatorFormData = z.infer<
  typeof mortgageCalculatorSchema
>;
export type ExpenseCalculationFormData = z.infer<
  typeof expenseCalculationSchema
>;
export type ListingSheetFormData = z.infer<typeof listingSheetSchema>;
