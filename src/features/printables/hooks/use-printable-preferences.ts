"use client";

import { useState, useCallback } from "react";
import { apiClient } from "@/shared/lib/api";
import { getCookie } from "@/shared/lib/auth";
import { useAuthContext } from "@/shared/contexts/auth-context";

export interface PrintablePreferences {
  photoOnPrintable: boolean;
  qrCodeOnPrintable: boolean;
  emptyPrintableFooter: boolean;
}

export interface UsePrintablePreferencesResult {
  preferences: PrintablePreferences;
  isLoading: boolean;
  error: string | null;
  updatePreferences: (newPreferences: Partial<PrintablePreferences>) => Promise<void>;
  setPhotoOnPrintable: () => Promise<void>;
  setQrCodeOnPrintable: () => Promise<void>;
  setEmptyFooter: () => Promise<void>;
}

/**
 * Hook for managing user preferences for printable PDF customization
 */
export function usePrintablePreferences(): UsePrintablePreferencesResult {
  const { userAuth, refreshUserData } = useAuthContext();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get current preferences from user context
  const preferences: PrintablePreferences = {
    photoOnPrintable: userAuth.userInfo?.photoOnPrintable ?? false,
    qrCodeOnPrintable: userAuth.userInfo?.qrCodeOnPrintable ?? false,
    emptyPrintableFooter: userAuth.userInfo?.emptyPrintableFooter ?? true,
  };

  /**
   * Updates user preferences on the server
   */
  const updatePreferences = useCallback(
    async (newPreferences: Partial<PrintablePreferences>) => {
      try {
        setIsLoading(true);
        setError(null);

        const userId = getCookie("userId");
        if (!userId) {
          throw new Error("User ID not found");
        }

        // Ensure only one preference is true at a time
        let finalPreferences: PrintablePreferences;
        if (newPreferences.photoOnPrintable) {
          finalPreferences = {
            photoOnPrintable: true,
            qrCodeOnPrintable: false,
            emptyPrintableFooter: false,
          };
        } else if (newPreferences.qrCodeOnPrintable) {
          finalPreferences = {
            photoOnPrintable: false,
            qrCodeOnPrintable: true,
            emptyPrintableFooter: false,
          };
        } else {
          finalPreferences = {
            photoOnPrintable: false,
            qrCodeOnPrintable: false,
            emptyPrintableFooter: true,
          };
        }

        const response = await apiClient.put(`/users/${userId}`, finalPreferences);

        if (!response.success) {
          throw new Error(response.error || "Failed to update preferences");
        }

        // Refresh user data to get updated preferences
        await refreshUserData();
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to update preferences";
        setError(errorMessage);
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [refreshUserData]
  );

  /**
   * Sets preference to show photo on printables
   */
  const setPhotoOnPrintable = useCallback(async () => {
    await updatePreferences({ photoOnPrintable: true });
  }, [updatePreferences]);

  /**
   * Sets preference to show QR code on printables
   */
  const setQrCodeOnPrintable = useCallback(async () => {
    await updatePreferences({ qrCodeOnPrintable: true });
  }, [updatePreferences]);

  /**
   * Sets preference to show empty footer on printables
   */
  const setEmptyFooter = useCallback(async () => {
    await updatePreferences({ emptyPrintableFooter: true });
  }, [updatePreferences]);

  return {
    preferences,
    isLoading,
    error,
    updatePreferences,
    setPhotoOnPrintable,
    setQrCodeOnPrintable,
    setEmptyFooter,
  };
}
