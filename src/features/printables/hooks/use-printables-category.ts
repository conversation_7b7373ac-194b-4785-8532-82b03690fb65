"use client";

import { useState, useEffect } from "react";
import { PrintableDocument, getPrintablesByProvince } from "../lib/api-client";

export interface UsePrintablesCategoryResult {
  items: PrintableDocument[];
  isLoading: boolean;
  error: string | null;
  retry: () => void;
}

export function usePrintablesCategory(category: string): UsePrintablesCategoryResult {
  const [items, setItems] = useState<PrintableDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPrintablesByCategory = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await getPrintablesByProvince(category);
      
      if (response.error) {
        setError(response.error);
        setItems([]);
      } else {
        setItems(response.data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch printables for this category");
      setItems([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (category) {
      fetchPrintablesByCategory();
    }
  }, [category]);

  const retry = () => {
    fetchPrintablesByCategory();
  };

  return {
    items,
    isLoading,
    error,
    retry,
  };
}
