"use client";

import { PDFDocument, rgb, StandardFonts } from "pdf-lib";
import { saveAs } from "file-saver";

/**
 * Fetches a PDF file from a URL
 */
export async function fetchPdf(url: string, maxRetries: number = 3): Promise<{
  arrayBuffer: ArrayBuffer;
  contentType: string;
}> {
  let lastError: Error | null = null;

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      console.log(`Fetching PDF (attempt ${attempt + 1}): ${url}`);

      const response = await fetch(url, {
        headers: {
          Accept: "application/pdf",
          "Accept-Encoding": "gzip, deflate, br",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const arrayBuffer = await response.arrayBuffer();
      const contentType = response.headers.get("content-type") || "application/pdf";

      return { arrayBuffer, contentType };
    } catch (error) {
      console.error(`Attempt ${attempt + 1} failed to fetch PDF:`, error);
      lastError = error instanceof Error ? error : new Error(String(error));

      // Wait before retrying
      if (attempt < maxRetries - 1) {
        await new Promise((resolve) => setTimeout(resolve, 1000 * (attempt + 1)));
      }
    }
  }

  throw lastError || new Error("Failed to fetch PDF after multiple attempts");
}

/**
 * Checks if the given ArrayBuffer is likely to be a HEIC image
 */
function isLikelyHEIC(arrayBuffer: ArrayBuffer): boolean {
  const uint8Array = new Uint8Array(arrayBuffer);
  const heicSignature = [0x66, 0x74, 0x79, 0x70, 0x68, 0x65, 0x69, 0x63]; // "ftypheic"

  for (let i = 4; i < uint8Array.length - 8; i++) {
    if (heicSignature.every((value, index) => uint8Array[i + index] === value)) {
      return true;
    }
  }
  return false;
}

/**
 * Fetches an image from a URL with retry logic
 */
export async function fetchImage(
  url: string,
  options: { maxRetries?: number; fallbackUrl?: string | null } = {}
): Promise<{ arrayBuffer: ArrayBuffer; contentType: string }> {
  const { maxRetries = 3, fallbackUrl = null } = options;

  const fetchWithRetry = async (attemptUrl: string) => {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        console.log(`Fetching image (attempt ${attempt + 1}): ${attemptUrl}`);

        const response = await fetch(attemptUrl, {
          headers: {
            Accept: "image/*",
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        let contentType = response.headers.get("content-type") || "image/jpeg";
        let arrayBuffer = await response.arrayBuffer();

        // Handle HEIC conversion if needed (note: heic-convert is not available in browser)
        if (contentType.includes("heic") || attemptUrl.toLowerCase().endsWith(".heic")) {
          console.warn("HEIC images are not supported in browser environment");
          // For now, we'll skip HEIC conversion in the browser
          // In a production environment, this should be handled server-side
        }

        return { arrayBuffer, contentType };
      } catch (error) {
        console.error(`Attempt ${attempt + 1} failed to fetch image:`, error);
        lastError = error instanceof Error ? error : new Error(String(error));

        // Wait before retrying
        if (attempt < maxRetries - 1) {
          await new Promise((resolve) => setTimeout(resolve, 1000 * (attempt + 1)));
        }
      }
    }

    // If all attempts fail and we have a fallback URL, try that
    if (fallbackUrl && attemptUrl !== fallbackUrl) {
      return fetchWithRetry(fallbackUrl);
    }

    throw lastError || new Error("Failed to fetch image after multiple attempts");
  };

  return fetchWithRetry(url);
}

/**
 * Determines the file extension from a URL
 */
export function getFileExtension(url: string, defaultExt: string = ".jpg"): string {
  if (!url) return defaultExt;
  const match = url.match(/\.([a-zA-Z0-9]+)(?:[?#]|$)/i);
  return match ? `.${match[1].toLowerCase()}` : defaultExt;
}

/**
 * Formats a phone number for display
 */
export function formatPhone(phone?: string): string {
  if (!phone) return "";
  const cleaned = phone.replace(/\D/g, "");
  if (cleaned.length >= 10) {
    return `${cleaned.slice(0, 3)}.${cleaned.slice(3, 6)}.${cleaned.slice(6, 10)}`;
  }
  return phone;
}

/**
 * Transforms a website URL for display (removes protocol and www)
 */
export function transformSiteURL(site: string): string {
  return site.replace(/^(https?:\/\/)?(www\.)?/, "");
}

/**
 * User interface for PDF modification
 */
export interface UserForPdf {
  firstname?: string;
  lastname?: string;
  titles?: string;
  position?: string;
  workEmail?: string;
  email?: string;
  workPhone?: string;
  phone?: string;
  website?: string;
  photo?: {
    url?: string;
    formats?: {
      small?: { url: string };
      squared?: { url: string };
      thumbnail?: { url: string };
    };
  };
  circularPhoto?: {
    url?: string;
  };
  photoOnPrintable?: boolean;
  qrCodeOnPrintable?: boolean;
  emptyPrintableFooter?: boolean;
}

/**
 * Contact position types
 */
export type ContactPosition = "footerLeft" | "columnRight" | "noContactInfo";

/**
 * Position configuration for text and image placement
 */
interface PositionConfig {
  name: { x: number; y: number; size: number; font: any; color: any };
  position: { x: number; y: number; size: number; font: any; color: any };
  email: { x: number; y: number; size: number; font: any; color: any };
  phone: { x: number; y: number; size: number; font: any; color: any };
  website: { x: number; y: number; size: number; font: any; color: any };
  photo: { x: number; y: number; width: number; height: number };
}

/**
 * Gets item positions for different contact positions
 */
export function getItemPositions(
  width: number,
  emptyPrintableFooter: boolean = false,
  font: any
): Record<ContactPosition, PositionConfig> {
  return {
    columnRight: {
      name: { x: width - 184, y: 75, size: 10, font, color: rgb(0.42, 0.61, 0.72) },
      position: { x: width - 184, y: 65, size: 8, font, color: rgb(0.42, 0.61, 0.72) },
      email: { x: width - 184, y: 51, size: 8, font, color: rgb(1, 1, 1) },
      phone: { x: width - 184, y: 39, size: 8, font, color: rgb(1, 1, 1) },
      website: { x: width - 184, y: 28, size: 8, font, color: rgb(1, 1, 1) },
      photo: { x: width - 184, y: 90, width: 40, height: 40 },
    },
    footerLeft: {
      name: { x: emptyPrintableFooter ? 24 : 94, y: 74, size: 12, font, color: rgb(0.42, 0.61, 0.72) },
      position: { x: emptyPrintableFooter ? 24 : 94, y: 60, size: 10, font, color: rgb(0.42, 0.61, 0.72) },
      email: { x: emptyPrintableFooter ? 24 : 94, y: 48, size: 10, font, color: rgb(1, 1, 1) },
      phone: { x: emptyPrintableFooter ? 24 : 94, y: 37, size: 10, font, color: rgb(1, 1, 1) },
      website: { x: emptyPrintableFooter ? 24 : 94, y: 25, size: 10, font, color: rgb(1, 1, 1) },
      photo: { x: 20, y: 26, width: 60, height: 60 },
    },
    noContactInfo: {
      name: { x: 0, y: 0, size: 0, font, color: rgb(0, 0, 0) },
      position: { x: 0, y: 0, size: 0, font, color: rgb(0, 0, 0) },
      email: { x: 0, y: 0, size: 0, font, color: rgb(0, 0, 0) },
      phone: { x: 0, y: 0, size: 0, font, color: rgb(0, 0, 0) },
      website: { x: 0, y: 0, size: 0, font, color: rgb(0, 0, 0) },
      photo: { x: 0, y: 0, width: 0, height: 0 },
    },
  };
}

/**
 * Validates required user data for PDF modification
 */
export function validateRequiredData(
  user: UserForPdf,
  contactPosition: ContactPosition
): string[] {
  const errors: string[] = [];

  // Basic user info validation
  if (!user.firstname || !user.lastname) {
    errors.push("Your first and last name are required for the document footer");
  }

  // Photo validation if photo option is enabled
  if (user.photoOnPrintable && contactPosition !== "noContactInfo") {
    if (!user.photo || (!user.photo.url && !user.photo.formats)) {
      errors.push("You've selected to show your photo, but no photo is available in your profile");
    }
  }

  return errors;
}

/**
 * Gets the appropriate image URL based on user preferences
 */
export async function getImageUrl(
  user: UserForPdf,
  qrUrlPath?: string | null
): Promise<string | null> {
  try {
    // Check for photo preference and availability
    if (user.photoOnPrintable && user.photo) {
      // First try squared photo (highest priority)
      if (user.photo.formats?.squared?.url) {
        console.log("Using squared photo:", user.photo.formats.squared.url);
        return user.photo.formats.squared.url;
      }

      // Then try small photo
      if (user.photo.formats?.small?.url) {
        console.log("Using small photo:", user.photo.formats.small.url);
        return user.photo.formats.small.url;
      }

      // Finally use original photo if nothing else is available
      if (user.photo.url) {
        console.log("Using original photo:", user.photo.url);
        return user.photo.url;
      }
    }

    // Check for QR code preference and availability
    if (user.qrCodeOnPrintable && qrUrlPath && qrUrlPath !== "null") {
      console.log("Using QR code:", qrUrlPath);
      return qrUrlPath;
    }

    return null;
  } catch (error) {
    console.error("Error getting image URL:", error);
    return null;
  }
}

/**
 * Main function to modify a PDF with user contact information
 */
export async function modifyPdf(
  pdfUrl: string,
  user: UserForPdf,
  contactPosition: ContactPosition,
  qrUrlPath?: string | null
): Promise<{ pdfBytes: Uint8Array; blobUrl: string } | null> {
  try {
    console.log("Starting PDF modification...", { pdfUrl, contactPosition, user });

    // Validate required data
    const validationErrors = validateRequiredData(user, contactPosition);
    if (validationErrors.length > 0) {
      throw new Error(`Validation failed: ${validationErrors.join(", ")}`);
    }

    // Fetch the PDF file
    const pdfResponse = await fetchPdf(pdfUrl);
    if (!pdfResponse.arrayBuffer) {
      throw new Error("Failed to fetch PDF file");
    }

    if (!pdfResponse.contentType.includes("pdf")) {
      throw new Error("Fetched file is not a PDF");
    }

    // Load the original PDF
    const originalPdf = await PDFDocument.load(pdfResponse.arrayBuffer, {
      updateMetadata: false, // Prevent metadata updates to preserve form fields
    });

    // For noContactInfo, just return the original PDF
    if (contactPosition === "noContactInfo") {
      const pdfBytes = await originalPdf.save({ updateMetadata: false });
      const blob = new Blob([pdfBytes], { type: "application/pdf" });
      const blobUrl = URL.createObjectURL(blob);
      return { pdfBytes, blobUrl };
    }

    // Get the last page for footer placement
    const pages = originalPdf.getPages();
    const lastPage = pages[pages.length - 1];
    const { width } = lastPage.getSize();

    // Prepare image if needed
    const imageUrl = await getImageUrl(user, qrUrlPath);
    let image: any = null;

    if (imageUrl) {
      try {
        const { arrayBuffer: imageBytes, contentType } = await fetchImage(imageUrl);
        console.log("Fetched image content type:", contentType);
        console.log("Image bytes length:", imageBytes.byteLength);

        if (!imageBytes || imageBytes.byteLength === 0) {
          console.error("Empty image data received");
        } else {
          if (contentType.includes("png")) {
            image = await originalPdf.embedPng(imageBytes);
            console.log("PNG image embedded successfully, dimensions:", image.width, "x", image.height);
          } else if (contentType.includes("jpeg") || contentType.includes("jpg")) {
            image = await originalPdf.embedJpg(imageBytes);
            console.log("JPEG image embedded successfully, dimensions:", image.width, "x", image.height);
          } else {
            console.warn("Unsupported image format:", contentType);
          }
        }
      } catch (imageError) {
        console.error("Error processing image:", imageError);
        // Continue without the image
      }
    }

    // Create font
    const helveticaFont = await originalPdf.embedFont(StandardFonts.Helvetica);

    // Get positions for drawing
    const itemPositions = getItemPositions(width, user.emptyPrintableFooter || false, helveticaFont);
    const pos = itemPositions[contactPosition];

    // Draw contact information on the last page
    try {
      // Draw name if available
      if (user.firstname && user.lastname) {
        const nameText = `${user.firstname} ${user.lastname}${
          user.titles ? ", " + user.titles : ""
        }`;
        lastPage.drawText(nameText, pos.name);
      }

      if (user.position) {
        lastPage.drawText(user.position, pos.position);
      }

      const email = user.workEmail || user.email;
      if (email) {
        lastPage.drawText(email, pos.email);
      }

      const phone = formatPhone(user.workPhone || user.phone);
      if (phone) {
        lastPage.drawText(phone, pos.phone);
      }

      if (user.website) {
        lastPage.drawText(transformSiteURL(user.website), pos.website);
      }

      // Draw image if available
      if ((user.photoOnPrintable || user.qrCodeOnPrintable) && image) {
        try {
          console.log("Drawing image with dimensions:", {
            x: pos.photo.x,
            y: pos.photo.y,
            width: pos.photo.width,
            height: pos.photo.height,
          });

          lastPage.drawImage(image, {
            x: pos.photo.x,
            y: pos.photo.y,
            width: pos.photo.width,
            height: pos.photo.height,
          });
          console.log("Image drawn successfully");
        } catch (drawError) {
          console.error("Error drawing image:", drawError);
        }
      }
    } catch (embedError) {
      console.error("Error adding footer content:", embedError);
      // Continue without the footer rather than failing completely
    }

    // Save the modified PDF
    const pdfBytes = await originalPdf.save({ updateMetadata: false });
    const blob = new Blob([pdfBytes], { type: "application/pdf" });
    const blobUrl = URL.createObjectURL(blob);

    console.log("PDF modification completed successfully");
    return { pdfBytes, blobUrl };
  } catch (error) {
    console.error("Error modifying PDF:", error);
    throw error;
  }
}

/**
 * Downloads a PDF file
 */
export function downloadPdf(blobUrl: string, filename: string = "printable.pdf"): void {
  saveAs(blobUrl, filename);
}
