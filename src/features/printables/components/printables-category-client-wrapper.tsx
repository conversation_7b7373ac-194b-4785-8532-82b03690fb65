"use client";

import React from "react";
import { useRouter } from "next/navigation";
import CategoryItemsList from "@/shared/components/category-items-list";
import { PrintableDocument, formatProvinceTitle } from "../lib/api-client";
import { usePrintablesCategory } from "../hooks/use-printables-category";

interface PrintablesCategoryClientWrapperProps {
  category: string;
}

const PrintablesCategoryClientWrapper: React.FC<PrintablesCategoryClientWrapperProps> = ({ category }) => {
  const router = useRouter();
  const { items, isLoading, error, retry } = usePrintablesCategory(category);

  const handleItemSelect = React.useCallback((item: any) => {
    // Navigate to the document detail page with all necessary parameters
    // The item is a CategoryItem, but we stored the original PrintableDocument in metadata
    const originalDocument = item.metadata?.originalDocument as PrintableDocument;
    const slug = item.metadata?.slug || originalDocument?.slug;
    
    if (!originalDocument) {
      console.error('Original document data not found for item:', item);
      return;
    }
    
    if (!slug) {
      console.error('Document slug not found for item:', originalDocument);
      return;
    }
    
    // Safely find the province file with proper null checks
    const provinceFile = originalDocument.provinceFile && Array.isArray(originalDocument.provinceFile) 
      ? originalDocument.provinceFile.find((pf) => pf.province === category)
      : undefined;
    
    if (provinceFile?.file) {
      const queryParams = new URLSearchParams({
        title: originalDocument.title,
        documentId: originalDocument.documentId,
        file: provinceFile.file.url,
        ext: provinceFile.file.ext.replace('.', ''),
        filetype: provinceFile.file.ext,
        position: originalDocument.contactPosition || 'unknown',
        // Note: qrId is not available in this context, so we'll pass 'null'
        // The document page will handle this appropriately
        qrId: 'null'
      });
      
      router.push(`/printables/document/${slug}?${queryParams.toString()}`);
    } else {
      // Fallback if no file is available
      const queryParams = new URLSearchParams({
        title: originalDocument.title,
        documentId: originalDocument.documentId,
        position: originalDocument.contactPosition || 'unknown',
        qrId: 'null'
      });
      
      // Add file info if available from the first provinceFile entry
      if (originalDocument.provinceFile && Array.isArray(originalDocument.provinceFile) && originalDocument.provinceFile.length > 0) {
        const firstProvinceFile = originalDocument.provinceFile[0];
        if (firstProvinceFile?.file) {
          queryParams.append('file', firstProvinceFile.file.url);
          queryParams.append('ext', firstProvinceFile.file.ext.replace('.', ''));
          queryParams.append('filetype', firstProvinceFile.file.ext);
        }
      }
      
      router.push(`/printables/document/${slug}?${queryParams.toString()}`);
    }
  }, [router, category]);

  const handleRetry = React.useCallback(() => {
    retry();
  }, [retry]);

  const handleSearch = React.useCallback((query: string) => {
    // Implement search functionality
    console.log("Search query:", query);
  }, []);

  const handleFilterChange = React.useCallback((filter: string) => {
    // Implement filter functionality
    console.log("Filter changed:", filter);
  }, []);

  const formattedTitle = formatProvinceTitle(category);

  // Transform items to include thumbnail and file information from the selected province
  const transformedItems = items.map((item) => {
    // Safely find the provinceFile entry for the current category with null checks
    const provinceFile = item.provinceFile && Array.isArray(item.provinceFile) 
      ? item.provinceFile.find((pf) => pf.province === category)
      : undefined;
    
    return {
      id: item.id.toString(),
      title: item.title,
      description: `Document ID: ${item.documentId}`,
      thumbnail: provinceFile?.thumbnail ? {
        url: provinceFile.thumbnail.url,
        alternativeText: provinceFile.thumbnail.alternativeText,
        formats: {
          thumbnail: {
            url: provinceFile.thumbnail.formats?.small?.url || provinceFile.thumbnail.url
          }
        }
      } : undefined,
      file: provinceFile?.file ? {
        url: provinceFile.file.url,
        ext: provinceFile.file.ext,
        size: provinceFile.file.size
      } : undefined,
      metadata: {
        slug: item.slug,
        documentId: item.documentId,
        province: category,
        createdAt: item.createdAt,
        originalDocument: item, // Store the original PrintableDocument
      },
      category: formattedTitle,
    };
  });

  return (
    <CategoryItemsList
      title={formattedTitle}
      subtitle={`Printable documents for ${formattedTitle.toLowerCase()}`}
      items={transformedItems}
      isLoading={isLoading}
      error={error}
      onItemSelect={handleItemSelect}
      buttonLabel="View Document"
      emptyStateMessage={`No printable documents found for ${formattedTitle.toLowerCase()}`}
      retryAction={handleRetry}
      showSearchAndFilter={true}
      onSearch={handleSearch}
      onFilterChange={handleFilterChange}
      availableFilters={[formattedTitle]}
    />
  );
};

export default PrintablesCategoryClientWrapper;
