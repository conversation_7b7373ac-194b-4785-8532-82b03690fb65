"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/shared/ui/card";
import { Button } from "@/shared/ui/button";
import { Badge } from "@/shared/ui/badge";
import { Skeleton } from "@/shared/ui/skeleton";
import { Alert, AlertDescription } from "@/shared/ui/alert";
import { AlertCircle } from "lucide-react";
import { usePrintableDocument } from "../hooks/use-printable-document";
import { formatProvinceTitle, type ContactPosition } from "../lib/api-client";
import PdfViewer from "./pdf-viewer";

interface PrintablesDocumentClientWrapperProps {
  documentId: string;
  title?: string;
  file?: string;
  ext?: string;
  filetype?: string;
  position?: string;
  qrId?: string;
}

const PrintablesDocumentClientWrapper: React.FC<PrintablesDocumentClientWrapperProps> = ({ 
  documentId, 
  title: urlTitle, 
  file: urlFile, 
  ext, 
  filetype, 
  position, 
  qrId 
}) => {
  const { document, isLoading, error, retry } = usePrintableDocument(documentId);

  if (isLoading) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <div className="space-y-4">
          <Skeleton className="h-10 w-1/3" />
          <Skeleton className="h-6 w-1/2" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-1/2" />
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-4">
                <Skeleton className="h-6 w-1/3" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                  <Skeleton className="h-4 w-2/3" />
                </div>
              </div>
              <div className="space-y-4">
                <Skeleton className="h-6 w-1/3" />
                <div className="space-y-2">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="text-center text-destructive">
              Error Loading Document
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center">
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={retry} variant="outline">
                Try again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!document) {
    return (
      <div className="container mx-auto py-6">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="text-center">Document Not Found</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center">
              <p className="text-muted-foreground mb-4">
                The requested document could not be found.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Get the first province file for display purposes
  const firstProvinceFile = document.provinceFile?.[0];
  // Use title from URL params if available, otherwise from document data
  const documentTitle = urlTitle || document.title || "Untitled Document";
  // Use file URL from URL params if available, otherwise from document data
  const documentFile = urlFile || firstProvinceFile?.file?.url;

  // Determine contact position, default to footerLeft if not specified
  const contactPos: ContactPosition = (position as ContactPosition) || "footerLeft";

  // If no PDF file is available, show error
  if (!documentFile) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            No PDF file is available for this document.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="space-y-4">
        <h1 className="text-3xl font-bold tracking-tight">{documentTitle}</h1>
        <p className="text-muted-foreground">
          Customize and download your printable document
        </p>
      </div>

      {/* Document Information Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Document Information</span>
            <Badge variant="secondary">Printable</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <p><span className="font-medium">Document ID:</span> {document.documentId}</p>
              <p><span className="font-medium">Slug:</span> {document.slug}</p>
              <p><span className="font-medium">Contact Position:</span> {contactPos}</p>
            </div>
            <div className="space-y-2">
              {document.provinceFile && document.provinceFile.length > 0 && (
                <p>
                  <span className="font-medium">Available for:</span>{" "}
                  {document.provinceFile.map((pf, index) => (
                    <span key={index}>
                      {formatProvinceTitle(pf.province)}
                      {index < document.provinceFile.length - 1 ? ", " : ""}
                    </span>
                  ))}
                </p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* PDF Viewer with customization options */}
      <PdfViewer
        pdfUrl={documentFile}
        title={documentTitle}
        contactPosition={contactPos}
        qrId={qrId}
      />
    </div>
  );
};

export default PrintablesDocumentClientWrapper;
