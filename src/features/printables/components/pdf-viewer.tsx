"use client";

import React, { useState, useEffect, useRef, useC<PERSON>back, useMemo } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/shared/ui/card";
import { <PERSON><PERSON> } from "@/shared/ui/button";
import { Badge } from "@/shared/ui/badge";
import { Alert, AlertDescription } from "@/shared/ui/alert";
import { Skeleton } from "@/shared/ui/skeleton";
import { RadioGroup, RadioGroupItem } from "@/shared/ui/radio-group";
import { Label } from "@/shared/ui/label";
import { Download, FileText, AlertCircle, Loader2 } from "lucide-react";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { usePrintablePreferences } from "../hooks/use-printable-preferences";
import { modifyPdf, downloadPdf, type UserForPdf, type ContactPosition } from "../lib/pdf-utils";

interface PdfViewerProps {
  pdfUrl: string;
  title: string;
  contactPosition: ContactPosition;
  qrId?: string | null;
  className?: string;
}

const PdfViewer: React.FC<PdfViewerProps> = ({
  pdfUrl,
  title,
  contactPosition,
  qrId,
  className,
}) => {
  const { userAuth } = useAuthContext();
  const { preferences, isLoading: preferencesLoading, updatePreferences } = usePrintablePreferences();
  
  const [pdfBlobUrl, setPdfBlobUrl] = useState<string | null>(null);
  const [pdfBytes, setPdfBytes] = useState<Uint8Array | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [iframeHeight, setIframeHeight] = useState<number>(800);
  
  const viewerRef = useRef<HTMLIFrameElement>(null);

  // Calculate iframe height based on window size
  const calculateIframeHeight = () => {
    if (typeof window !== "undefined") {
      return Math.max(600, (window.innerHeight - 200) * 1.41);
    }
    return 800;
  };

  // Convert user data to PDF format
  const convertUserToPdfFormat = useCallback((user: any): UserForPdf => {
    return {
      firstname: user?.firstname,
      lastname: user?.lastname,
      titles: user?.titles,
      position: user?.position,
      workEmail: user?.workEmail,
      email: user?.email,
      workPhone: user?.workPhone,
      phone: user?.phone,
      website: user?.website,
      photo: user?.photo,
      circularPhoto: user?.circularPhoto,
      photoOnPrintable: preferences.photoOnPrintable,
      qrCodeOnPrintable: preferences.qrCodeOnPrintable,
      emptyPrintableFooter: preferences.emptyPrintableFooter,
    };
  }, [preferences]);

  // Process PDF with user information
  const processPdf = useCallback(async () => {
    if (!userAuth.userInfo || !pdfUrl) return;

    setIsProcessing(true);
    setError(null);
    setValidationErrors([]);

    try {
      const userForPdf = convertUserToPdfFormat(userAuth.userInfo);
      const result = await modifyPdf(pdfUrl, userForPdf, contactPosition, qrId);

      if (result) {
        setPdfBlobUrl(result.blobUrl);
        setPdfBytes(result.pdfBytes);
      }
    } catch (err) {
      console.error("Error processing PDF:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to process PDF";

      if (errorMessage.includes("Validation failed:")) {
        const validationMessage = errorMessage.replace("Validation failed: ", "");
        setValidationErrors(validationMessage.split(", "));
      } else {
        setError(errorMessage);
      }
    } finally {
      setIsProcessing(false);
    }
  }, [userAuth.userInfo, pdfUrl, contactPosition, qrId, preferences]);

  // Handle preference changes
  const handlePreferenceChange = useCallback(async (value: string) => {
    try {
      switch (value) {
        case "photo":
          await updatePreferences({ photoOnPrintable: true });
          break;
        case "qrcode":
          await updatePreferences({ qrCodeOnPrintable: true });
          break;
        case "empty":
          await updatePreferences({ emptyPrintableFooter: true });
          break;
      }
    } catch (err) {
      console.error("Error updating preferences:", err);
      setError("Failed to update preferences");
    }
  }, [updatePreferences]);

  // Download the processed PDF
  const handleDownload = useCallback(() => {
    if (pdfBlobUrl) {
      const filename = `${title.replace(/\s+/g, "-").toLowerCase()}.pdf`;
      downloadPdf(pdfBlobUrl, filename);
    }
  }, [pdfBlobUrl, title]);

  // Process PDF when preferences change
  useEffect(() => {
    if (!preferencesLoading && userAuth.userInfo) {
      processPdf();
    }
  }, [processPdf, preferencesLoading, userAuth.userInfo]);

  // Update iframe height on window resize
  useEffect(() => {
    const updateHeight = () => {
      setIframeHeight(calculateIframeHeight());
    };

    updateHeight();
    window.addEventListener("resize", updateHeight);
    return () => window.removeEventListener("resize", updateHeight);
  }, []);

  // Get current preference value for radio group
  const currentPreference = useMemo(() => {
    if (preferences.photoOnPrintable) return "photo";
    if (preferences.qrCodeOnPrintable) return "qrcode";
    return "empty";
  }, [preferences]);

  if (!userAuth.userInfo) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Please log in to view and customize printable documents.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Preferences Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Printable Customization
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              What would you like to show on the printable footer?
            </p>
            
            <RadioGroup
              value={currentPreference}
              onValueChange={handlePreferenceChange}
              disabled={preferencesLoading || isProcessing}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="empty" id="empty" />
                <Label htmlFor="empty">No Photo or QR Code (Empty)</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="photo" id="photo" />
                <Label htmlFor="photo">My Profile Photo</Label>
              </div>
              
              {/* Only show QR code option if user has QR codes */}
              {qrId && qrId !== "null" && (
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="qrcode" id="qrcode" />
                  <Label htmlFor="qrcode">QR Code</Label>
                </div>
              )}
            </RadioGroup>
          </div>
        </CardContent>
      </Card>

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium">Please fix the following issues:</p>
              <ul className="list-disc list-inside space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index} className="text-sm">{error}</li>
                ))}
              </ul>
              <p className="text-sm">
                You can update your profile information in your account settings.
              </p>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* PDF Viewer */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {title}
            </span>
            <div className="flex items-center gap-2">
              <Badge variant="secondary">PDF</Badge>
              {pdfBlobUrl && (
                <Button onClick={handleDownload} size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isProcessing ? (
            <div className="flex items-center justify-center p-8">
              <div className="flex items-center gap-2">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span>Processing PDF...</span>
              </div>
            </div>
          ) : pdfBlobUrl ? (
            <div className="border rounded-lg overflow-hidden">
              <iframe
                ref={viewerRef}
                src={pdfBlobUrl}
                width="100%"
                height={iframeHeight}
                title={`PDF Viewer - ${title}`}
                className="border-0"
              />
            </div>
          ) : (
            <Skeleton className="w-full h-96" />
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default PdfViewer;
