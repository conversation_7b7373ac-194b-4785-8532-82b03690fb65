"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { CategoryItemsList } from "@/shared/components";
import { DocumentItem } from "@/shared/lib/category-api-client";
import { usePrintablesCategory } from "../hooks/use-printables-category";

interface PrintablesCategoryItemsProps {
  category: string;
}

const PrintablesCategoryItems: React.FC<PrintablesCategoryItemsProps> = ({ category }) => {
  const router = useRouter();
  const { items, isLoading, error, retry } = usePrintablesCategory(category);

  const handleItemSelect = (item: DocumentItem) => {
    // Navigate to the document detail page
    const slug = item.title.replace(/\s+/g, "-").toLowerCase();
    router.push(`/printables/document/${slug}?title=${slug}&file=${item.file?.url}&ext=${item.file?.ext?.replace(".", "")}`);
  };

  const formatCategoryTitle = (categoryName: string) => {
    return categoryName.replace(/([A-Z])/g, " $1").replace(/^./, (str) => str.toUpperCase());
  };

  return (
    <CategoryItemsList
      title={`${formatCategoryTitle(category)} Printable Documents`}
      subtitle={`Printable documents applicable for ${category}`}
      items={items.map((item) => ({
        id: item.id,
        title: item.title,
        description: item.description,
        thumbnail: item.thumbnail,
        file: item.file,
        metadata: item.metadata,
        category: category,
      }))}
      isLoading={isLoading}
      error={error}
      onItemSelect={handleItemSelect}
      buttonLabel="View Printable"
      emptyStateMessage={`No printable documents found for ${category}`}
      retryAction={retry}
      showSearchAndFilter={true}
      onSearch={(query) => {
        // Implement search functionality
        console.log("Search query:", query);
      }}
      onFilterChange={(filter) => {
        // Implement filter functionality
        console.log("Filter changed:", filter);
      }}
      availableFilters={[category]}
    />
  );
};

export default PrintablesCategoryItems;
