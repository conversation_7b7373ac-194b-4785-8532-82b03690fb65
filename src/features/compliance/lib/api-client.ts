"use client";

import { apiClient } from "@/shared/lib/api";
import { CategoryItem } from "@/shared/components/category-landing-page";

// Re-use the same interfaces from resources since the data structure is identical
export interface ProvinceFile {
  id: number;
  province: string;
  thumbnail: {
    id: number;
    name: string;
    alternativeText: string;
    caption: string;
    width: number | null;
    height: number | null;
    formats: {
      small?: { url: string; width: number; height: number };
      medium?: { url: string; width: number; height: number };
      squared?: { url: string; width: number; height: number };
      thumbnail?: { url: string; width: number; height: number };
    };
    hash: string;
    ext: string;
    mime: string;
    size: number;
    url: string;
    previewUrl: string | null;
    provider: string;
    provider_metadata: any;
    createdAt: string;
    updatedAt: string;
    documentId: string;
    publishedAt: string;
  };
  file: {
    id: number;
    name: string;
    alternativeText: string;
    caption: string;
    formats: any[];
    hash: string;
    ext: string;
    mime: string;
    size: number;
    url: string;
    previewUrl: string | null;
    provider: string;
    provider_metadata: any;
    createdAt: string;
    updatedAt: string;
    documentId: string;
    publishedAt: string;
  };
}

export interface ComplianceDocument {
  id: number;
  title: string;
  slug: string;
  documentType: string;
  isHidden: boolean;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  documentId: string;
  provinceFile: ProvinceFile[];
}

export interface ComplianceResponse {
  data: ComplianceDocument[];
  meta?: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

export interface ComplianceDocumentResponse {
  data: ComplianceDocument;
}

/**
 * Format province name for display
 */
export function formatProvinceTitle(province: string): string {
  if (province === "allProvinces") {
    return "All Provinces";
  }
  
  // Convert camelCase to Title Case
  return province
    .replace(/([A-Z])/g, " $1")
    .replace(/^./, (str) => str.toUpperCase())
    .trim();
}

/**
 * API client for compliance documents
 */
export class ComplianceApiClient {
  static readonly BASE_PATH = "/api/docs";

  /**
   * Get all compliance documents
   * Filters by documentType=compliance and populates provinceFile data
   */
  static async getCompliance(params?: {
    page?: number;
    pageSize?: number;
  }): Promise<{ data: ComplianceDocument[]; error?: string }> {
    try {
      const queryParams = new URLSearchParams();
      
      // Filter by document type
      queryParams.append("filters[documentType][$eq]", "compliance");
      
      // Filter by hidden status
      queryParams.append("filters[isHidden][$eq]", "false");
      
      // Populate provinceFile data
      queryParams.append("populate[0]", "provinceFile");
      queryParams.append("populate[1]", "provinceFile.file");
      queryParams.append("populate[2]", "provinceFile.thumbnail");
      
      // Pagination
      if (params?.page) {
        queryParams.append("pagination[page]", params.page.toString());
      }
      if (params?.pageSize) {
        queryParams.append("pagination[pageSize]", params.pageSize.toString());
      }

      const url = `${ComplianceApiClient.BASE_PATH}?${queryParams.toString()}`;
      const response = await apiClient.get<ComplianceResponse>(url);

      if (response.success && response.data) {
        return { data: response.data.data || [] };
      }

      return {
        data: [],
        error: response.error || "Failed to fetch compliance documents",
      };
    } catch (error) {
      console.error("Error fetching compliance documents:", error);
      return {
        data: [],
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch compliance documents",
      };
    }
  }

  /**
   * Get a specific compliance document by documentId
   */
  static async getComplianceDocument(
    documentId: string
  ): Promise<{ data?: ComplianceDocument; error?: string }> {
    try {
      if (!documentId || documentId.trim() === '') {
        return { error: "Invalid document ID" };
      }

      const queryParams = new URLSearchParams();
      queryParams.append("filters[documentId][$eq]", documentId);
      queryParams.append("filters[documentType][$eq]", "compliance");
      queryParams.append("populate[0]", "provinceFile");
      queryParams.append("populate[1]", "provinceFile.file");
      queryParams.append("populate[2]", "provinceFile.thumbnail");

      const response = await apiClient.get<ComplianceResponse>(
        `${ComplianceApiClient.BASE_PATH}?${queryParams.toString()}`
      );

      if (response.success && response.data?.data?.length > 0) {
        return { data: response.data.data[0] };
      }

      return {
        error: response.error || "Compliance document not found",
      };
    } catch (error) {
      console.error("Error fetching compliance document:", error);
      return {
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch compliance document",
      };
    }
  }

  /**
   * Get compliance documents by province
   */
  static async getComplianceByProvince(
    province: string
  ): Promise<{ data: ComplianceDocument[]; error?: string }> {
    try {
      const response = await ComplianceApiClient.getCompliance();
      
      if (response.error) {
        return response;
      }

      // Filter documents that have files for the specified province
      const filteredDocuments = response.data.filter((doc) =>
        doc.provinceFile.some((pf) => pf.province === province)
      );

      return { data: filteredDocuments };
    } catch (error) {
      console.error("Error fetching compliance documents by province:", error);
      return {
        data: [],
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch compliance documents by province",
      };
    }
  }

  /**
   * Get all available provinces for compliance documents
   */
  static async getComplianceProvinces(): Promise<{ data: string[]; error?: string }> {
    try {
      const queryParams = new URLSearchParams();
      queryParams.append("filters[documentType][$eq]", "compliance");
      queryParams.append("filters[isHidden][$eq]", "false");
      queryParams.append("populate[0]", "provinceFile");

      const url = `${ComplianceApiClient.BASE_PATH}?${queryParams.toString()}`;
      const response = await apiClient.get<ComplianceResponse>(url);

      if (response.success && response.data) {
        const provinces = new Set<string>();
        
        response.data.data.forEach((doc) => {
          doc.provinceFile.forEach((pf) => {
            provinces.add(pf.province);
          });
        });

        return { data: Array.from(provinces) };
      }

      return {
        data: [],
        error: response.error || "Failed to fetch compliance provinces",
      };
    } catch (error) {
      console.error("Error fetching compliance provinces:", error);
      return {
        data: [],
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch compliance provinces",
      };
    }
  }
}

/**
 * Transform compliance documents to categories for the landing page
 */
export function transformDocumentsToCategories(documents: ComplianceDocument[]): CategoryItem[] {
  const provinceMap = new Map<string, CategoryItem>();

  documents.forEach((doc) => {
    doc.provinceFile.forEach((pf) => {
      const province = pf.province;
      
      if (!provinceMap.has(province)) {
        provinceMap.set(province, {
          id: province,
          title: formatProvinceTitle(province),
          description: `Compliance documents for ${formatProvinceTitle(province).toLowerCase()}`,
          thumbnail: pf.thumbnail?.url,
          count: 0,
        });
      }
      
      const category = provinceMap.get(province)!;
      category.count = (category.count || 0) + 1;
    });
  });

  return Array.from(provinceMap.values()).sort((a, b) => {
    // Sort "All Provinces" first, then alphabetically
    if (a.id === "allProvinces") return -1;
    if (b.id === "allProvinces") return 1;
    return a.title.localeCompare(b.title);
  });
}

// Export convenience functions
export const getCompliance = ComplianceApiClient.getCompliance;
export const getComplianceDocument = ComplianceApiClient.getComplianceDocument;
export const getComplianceByProvince = ComplianceApiClient.getComplianceByProvince;
