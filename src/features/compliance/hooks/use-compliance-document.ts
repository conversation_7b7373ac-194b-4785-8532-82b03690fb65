"use client";

import { useState, useEffect } from "react";
import { ComplianceDocument, getComplianceDocument } from "../lib/api-client";

export interface UseComplianceDocumentResult {
  document?: ComplianceDocument;
  isLoading: boolean;
  error: string | null;
  retry: () => void;
}

export function useComplianceDocument(documentId: string): UseComplianceDocumentResult {
  const [document, setDocument] = useState<ComplianceDocument | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDocument = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      if (!documentId || documentId === 'undefined') {
        setError("Invalid document ID");
        setDocument(undefined);
        return;
      }
      
      const response = await getComplianceDocument(documentId);
      
      if (response.error) {
        setError(response.error);
        setDocument(undefined);
      } else {
        setDocument(response.data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch compliance document");
      setDocument(undefined);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (documentId) {
      fetchDocument();
    }
  }, [documentId]);

  const retry = () => {
    fetchDocument();
  };

  return {
    document,
    isLoading,
    error,
    retry,
  };
}
