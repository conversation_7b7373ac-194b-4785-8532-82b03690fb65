"use client";

import { useState, useEffect } from "react";
import { CategoryItem } from "@/shared/components/category-landing-page";
import { ComplianceDocument, getCompliance, transformDocumentsToCategories } from "../lib/api-client";

export interface UseComplianceResult {
  categories: CategoryItem[];
  documents: ComplianceDocument[];
  isLoading: boolean;
  error: string | null;
  retry: () => void;
}

export function useCompliance(): UseComplianceResult {
  const [documents, setDocuments] = useState<ComplianceDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCompliance = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await getCompliance();
      
      if (response.error) {
        setError(response.error);
        setDocuments([]);
      } else {
        setDocuments(response.data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch compliance documents");
      setDocuments([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCompliance();
  }, []);

  const retry = () => {
    fetchCompliance();
  };

  // Transform documents to categories
  const categories = transformDocumentsToCategories(documents);

  return {
    categories,
    documents,
    isLoading,
    error,
    retry,
  };
}
