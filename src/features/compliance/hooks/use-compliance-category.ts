"use client";

import { useState, useEffect } from "react";
import { ComplianceDocument, getComplianceByProvince } from "../lib/api-client";

export interface UseComplianceCategoryResult {
  items: ComplianceDocument[];
  isLoading: boolean;
  error: string | null;
  retry: () => void;
}

export function useComplianceCategory(category: string): UseComplianceCategoryResult {
  const [items, setItems] = useState<ComplianceDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchComplianceByCategory = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await getComplianceByProvince(category);
      
      if (response.error) {
        setError(response.error);
        setItems([]);
      } else {
        setItems(response.data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch compliance documents for this category");
      setItems([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (category) {
      fetchComplianceByCategory();
    }
  }, [category]);

  const retry = () => {
    fetchComplianceByCategory();
  };

  return {
    items,
    isLoading,
    error,
    retry,
  };
}
