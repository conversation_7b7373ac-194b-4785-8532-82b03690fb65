"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/shared/ui/card";
import { Badge } from "@/shared/ui/badge";
import { Skeleton } from "@/shared/ui/skeleton";
import { Alert, AlertDescription } from "@/shared/ui/alert";
import { AlertCircle } from "lucide-react";
import { Button } from "@/shared/ui/button";
import { useComplianceDocument } from "../hooks/use-compliance-document";
import { formatProvinceTitle } from "../lib/api-client";
import { DocumentViewer } from "@/features/resources";

interface ComplianceDocumentClientWrapperProps {
  documentId: string;
  title?: string;
  file?: string;
  ext?: string;
  filetype?: string;
}

const ComplianceDocumentClientWrapper: React.FC<ComplianceDocumentClientWrapperProps> = ({ 
  documentId, 
  title: urlTitle, 
  file: urlFile, 
  ext, 
  filetype,
}) => {
  const { document, isLoading, error, retry } = useComplianceDocument(documentId);

  if (isLoading) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <div className="space-y-4">
          <Skeleton className="h-10 w-1/3" />
          <Skeleton className="h-6 w-1/2" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-1/2" />
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-4">
                <Skeleton className="h-6 w-1/3" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                  <Skeleton className="h-4 w-2/3" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium">Error Loading Document</p>
              <p>{error}</p>
              <Button onClick={retry} variant="outline" size="sm">
                Try Again
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!document) {
    return (
      <div className="container mx-auto py-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            The requested compliance document could not be found.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Get the first province file for display purposes
  const firstProvinceFile = document.provinceFile?.[0];
  // Use title from URL params if available, otherwise from document data
  const documentTitle = urlTitle || document.title || "Untitled Document";
  // Use file URL from URL params if available, otherwise from document data
  const documentFile = urlFile || firstProvinceFile?.file?.url;
  // Use file type from URL params if available, otherwise from document data
  const documentFileType = filetype || ext || firstProvinceFile?.file?.ext;

  // If no file is available, show error
  if (!documentFile) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            No file is available for this document.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="space-y-4">
        <h1 className="text-3xl font-bold tracking-tight">{documentTitle}</h1>
        <p className="text-muted-foreground">
          View and download the compliance document
        </p>
      </div>

      {/* Document Information Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Document Information</span>
            <Badge variant="secondary">Compliance</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <p><span className="font-medium">Document ID:</span> {document.documentId}</p>
              <p><span className="font-medium">Slug:</span> {document.slug}</p>
              <p><span className="font-medium">Type:</span> Compliance Document</p>
            </div>
            <div className="space-y-2">
              {document.provinceFile && document.provinceFile.length > 0 && (
                <p>
                  <span className="font-medium">Available for:</span>{" "}
                  {document.provinceFile.map((pf, index) => (
                    <span key={index}>
                      {formatProvinceTitle(pf.province)}
                      {index < document.provinceFile.length - 1 ? ", " : ""}
                    </span>
                  ))}
                </p>
              )}
              {documentFileType && (
                <p><span className="font-medium">File Type:</span> {documentFileType}</p>
              )}
              {firstProvinceFile?.file?.size && (
                <p><span className="font-medium">File Size:</span> {Math.round(firstProvinceFile.file.size / 1024)} KB</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Document Viewer */}
      <DocumentViewer
        fileUrl={documentFile}
        title={documentTitle}
        fileType={documentFileType}
      />
    </div>
  );
};

export default ComplianceDocumentClientWrapper;
