"use client";

import React from "react";
import { useRouter } from "next/navigation";
import CategoryItemsList from "@/shared/components/category-items-list";
import { CategoryItemsListItem } from "@/shared/components/category-items-list";
import { useComplianceCategory } from "../hooks/use-compliance-category";
import { formatProvinceTitle } from "../lib/api-client";

interface ComplianceCategoryClientWrapperProps {
  category: string;
}

const ComplianceCategoryClientWrapper: React.FC<ComplianceCategoryClientWrapperProps> = ({
  category,
}) => {
  const router = useRouter();
  const { items, isLoading, error, retry } = useComplianceCategory(category);

  const handleItemSelect = React.useCallback((item: CategoryItemsListItem) => {
    // Find the original document to get the documentId
    const originalDocument = items.find((doc) => doc.id.toString() === item.id);
    
    if (!originalDocument) {
      console.error("Could not find original document for item:", item);
      return;
    }

    // Create slug from title
    const slug = originalDocument.title.replace(/\s+/g, "-").toLowerCase();
    
    // Find the province file for this category
    const provinceFile = originalDocument.provinceFile.find(
      (pf) => pf.province === category
    );
    
    if (provinceFile?.file) {
      const queryParams = new URLSearchParams({
        title: originalDocument.title,
        documentId: originalDocument.documentId,
        file: provinceFile.file.url,
        ext: provinceFile.file.ext.replace('.', ''),
        filetype: provinceFile.file.ext,
      });
      
      router.push(`/compliance/document/${slug}?${queryParams.toString()}`);
    } else {
      // Fallback if no file is available
      const queryParams = new URLSearchParams({
        title: originalDocument.title,
        documentId: originalDocument.documentId,
      });
      
      router.push(`/compliance/document/${slug}?${queryParams.toString()}`);
    }
  }, [items, category, router]);

  const handleRetry = React.useCallback(() => {
    retry();
  }, [retry]);

  const handleSearch = React.useCallback((query: string) => {
    // Implement search functionality if needed
    console.log("Search query:", query);
  }, []);

  const handleFilterChange = React.useCallback((filter: string) => {
    // Implement filter functionality if needed
    console.log("Filter changed:", filter);
  }, []);

  // Transform items for the CategoryItemsList component
  const transformedItems: CategoryItemsListItem[] = items.map((doc) => {
    // Find the province file for this category
    const provinceFile = doc.provinceFile.find((pf) => pf.province === category);
    
    return {
      id: doc.id.toString(),
      title: doc.title,
      description: `Compliance document for ${formatProvinceTitle(category).toLowerCase()}`,
      thumbnail: provinceFile?.thumbnail?.url || "/images/ico-pdf.svg",
      file: provinceFile?.file,
      metadata: {
        documentType: doc.documentType,
        province: category,
        fileType: provinceFile?.file?.ext || "unknown",
        fileSize: provinceFile?.file?.size || 0,
      },
      category: category,
    };
  });

  const formattedTitle = formatProvinceTitle(category);

  return (
    <CategoryItemsList
      title={`${formattedTitle} Compliance Documents`}
      subtitle={`Compliance documents for ${formattedTitle.toLowerCase()}`}
      items={transformedItems}
      isLoading={isLoading}
      error={error}
      onItemSelect={handleItemSelect}
      buttonLabel="View Document"
      emptyStateMessage={`No compliance documents found for ${formattedTitle.toLowerCase()}`}
      retryAction={handleRetry}
      showSearchAndFilter={true}
      onSearch={handleSearch}
      onFilterChange={handleFilterChange}
      availableFilters={[formattedTitle]}
    />
  );
};

export default ComplianceCategoryClientWrapper;
