import { useState, useCallback } from "react";
import { apiClient } from "@/shared/lib/api";
import type { ProfileData, ProfileFormData } from "../types";
import { getCookie } from "@/shared/lib/auth";

interface UseProfileResult {
  profile: ProfileData | null;
  isLoading: boolean;
  error: string | null;
  updateProfile: (payload: ProfileFormData) => Promise<void>;
  uploadPhoto: (file: File) => Promise<void>;
  refetchProfile: () => Promise<void>;
}

export function useProfile(): UseProfileResult {
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const refetchProfile = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await apiClient.get<ProfileData>(
        "/users-permissions/users/me"
      );

      if (response.success) {
        // Response is now a direct object, not wrapped in data property
        setProfile(response.data || null);
      } else {
        setError(response.error || "Failed to fetch profile");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch profile");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateProfile = useCallback(async (payload: ProfileFormData) => {
    try {
      setIsLoading(true);
      setError(null);

      // Get user ID from cookies
      const userId = getCookie("userId");
      if (!userId) {
        throw new Error("User ID not found");
      }

      const response = await apiClient.put<ProfileData>(
        `/users/${userId}`,
        payload
      );

      if (response.success) {
        setProfile(response.data);
      } else {
        setError(response.error || "Failed to update profile");
        throw new Error(response.error || "Failed to update profile");
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to update profile";
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const uploadPhoto = useCallback(
    async (file: File) => {
      try {
        setIsLoading(true);
        setError(null);

        const formData = new FormData();
        formData.append("files", file); // Strapi expects 'files' as the key

        // Get JWT token from cookies
        const jwt = getCookie("jwt");
        if (!jwt) throw new Error("Not authenticated");

        // Upload to Strapi V5 upload endpoint
        const uploadResponse = await fetch(
          `${
            process.env.NEXT_PUBLIC_STRAPI_URL || "http://localhost:1339"
          }/api/upload`,
          {
            method: "POST",
            headers: {
              Authorization: `Bearer ${jwt}`,
            },
            body: formData,
          }
        );

        if (!uploadResponse.ok) {
          throw new Error(`HTTP error! status: ${uploadResponse.status}`);
        }

        const uploadData = await uploadResponse.json();
        // Optionally, you may want to update the user profile with the uploaded file's ID
        // For now, just refetch the profile to get the new photo
        await refetchProfile();
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to upload photo";
        setError(errorMessage);
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [refetchProfile]
  );

  return {
    profile,
    isLoading,
    error,
    updateProfile,
    uploadPhoto,
    refetchProfile,
  };
}
