"use client";

import { apiClient } from "@/shared/lib/api";
import { CategoryItem } from "@/shared/components/category-landing-page";

// Re-use the same interfaces from printables since the data structure is identical
export interface ProvinceFile {
  id: number;
  province: string;
  thumbnail: {
    id: number;
    name: string;
    alternativeText: string;
    caption: string;
    width: number | null;
    height: number | null;
    formats: {
      small?: { url: string; width: number; height: number };
      medium?: { url: string; width: number; height: number };
      squared?: { url: string; width: number; height: number };
      thumbnail?: { url: string; width: number; height: number };
    };
    hash: string;
    ext: string;
    mime: string;
    size: number;
    url: string;
    previewUrl: string | null;
    provider: string;
    provider_metadata: any;
    createdAt: string;
    updatedAt: string;
    documentId: string;
    publishedAt: string;
  };
  file: {
    id: number;
    name: string;
    alternativeText: string;
    caption: string;
    formats: any[];
    hash: string;
    ext: string;
    mime: string;
    size: number;
    url: string;
    previewUrl: string | null;
    provider: string;
    provider_metadata: any;
    createdAt: string;
    updatedAt: string;
    documentId: string;
    publishedAt: string;
  };
}

export interface ResourceDocument {
  id: number;
  title: string;
  slug: string;
  documentType: string;
  isHidden: boolean;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  documentId: string;
  provinceFile: ProvinceFile[];
}

export interface ResourcesResponse {
  data: ResourceDocument[];
  meta?: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

export interface ResourceDocumentResponse {
  data: ResourceDocument;
}

/**
 * Format province name for display
 */
export function formatProvinceTitle(province: string): string {
  if (province === "allProvinces") {
    return "All Provinces";
  }
  
  // Convert camelCase to Title Case
  return province
    .replace(/([A-Z])/g, " $1")
    .replace(/^./, (str) => str.toUpperCase())
    .trim();
}

/**
 * API client for resources
 */
export class ResourcesApiClient {
  static readonly BASE_PATH = "/api/docs";

  /**
   * Get all resource documents
   * Filters by documentType=resource and populates provinceFile data
   */
  static async getResources(params?: {
    page?: number;
    pageSize?: number;
  }): Promise<{ data: ResourceDocument[]; error?: string }> {
    try {
      const queryParams = new URLSearchParams();
      
      // Filter by document type
      queryParams.append("filters[documentType][$eq]", "resource");
      
      // Filter by hidden status
      queryParams.append("filters[isHidden][$eq]", "false");
      
      // Populate provinceFile data
      queryParams.append("populate[0]", "provinceFile");
      queryParams.append("populate[1]", "provinceFile.file");
      queryParams.append("populate[2]", "provinceFile.thumbnail");
      
      // Pagination
      if (params?.page) {
        queryParams.append("pagination[page]", params.page.toString());
      }
      if (params?.pageSize) {
        queryParams.append("pagination[pageSize]", params.pageSize.toString());
      }

      const url = `${ResourcesApiClient.BASE_PATH}?${queryParams.toString()}`;
      const response = await apiClient.get<ResourcesResponse>(url);

      if (response.success && response.data) {
        return { data: response.data.data || [] };
      }

      return {
        data: [],
        error: response.error || "Failed to fetch resources",
      };
    } catch (error) {
      console.error("Error fetching resources:", error);
      return {
        data: [],
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch resources",
      };
    }
  }

  /**
   * Get a specific resource document by documentId
   */
  static async getResourceDocument(
    documentId: string
  ): Promise<{ data?: ResourceDocument; error?: string }> {
    try {
      if (!documentId || documentId.trim() === '') {
        return { error: "Invalid document ID" };
      }

      const queryParams = new URLSearchParams();
      queryParams.append("filters[documentId][$eq]", documentId);
      queryParams.append("filters[documentType][$eq]", "resource");
      queryParams.append("populate[0]", "provinceFile");
      queryParams.append("populate[1]", "provinceFile.file");
      queryParams.append("populate[2]", "provinceFile.thumbnail");

      const response = await apiClient.get<ResourcesResponse>(
        `${ResourcesApiClient.BASE_PATH}?${queryParams.toString()}`
      );

      if (response.success && response.data?.data?.length > 0) {
        return { data: response.data.data[0] };
      }

      return {
        error: response.error || "Resource document not found",
      };
    } catch (error) {
      console.error("Error fetching resource document:", error);
      return {
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch resource document",
      };
    }
  }

  /**
   * Get resource documents by province
   */
  static async getResourcesByProvince(
    province: string
  ): Promise<{ data: ResourceDocument[]; error?: string }> {
    try {
      const response = await ResourcesApiClient.getResources();
      
      if (response.error) {
        return response;
      }

      // Filter documents that have files for the specified province
      const filteredDocuments = response.data.filter((doc) =>
        doc.provinceFile.some((pf) => pf.province === province)
      );

      return { data: filteredDocuments };
    } catch (error) {
      console.error("Error fetching resources by province:", error);
      return {
        data: [],
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch resources by province",
      };
    }
  }

  /**
   * Get all available provinces for resources
   */
  static async getResourceProvinces(): Promise<{ data: string[]; error?: string }> {
    try {
      const queryParams = new URLSearchParams();
      queryParams.append("filters[documentType][$eq]", "resource");
      queryParams.append("filters[isHidden][$eq]", "false");
      queryParams.append("populate[0]", "provinceFile");

      const url = `${ResourcesApiClient.BASE_PATH}?${queryParams.toString()}`;
      const response = await apiClient.get<ResourcesResponse>(url);

      if (response.success && response.data) {
        const provinces = new Set<string>();
        
        response.data.data.forEach((doc) => {
          doc.provinceFile.forEach((pf) => {
            provinces.add(pf.province);
          });
        });

        return { data: Array.from(provinces) };
      }

      return {
        data: [],
        error: response.error || "Failed to fetch resource provinces",
      };
    } catch (error) {
      console.error("Error fetching resource provinces:", error);
      return {
        data: [],
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch resource provinces",
      };
    }
  }
}

/**
 * Transform resource documents to categories for the landing page
 */
export function transformDocumentsToCategories(documents: ResourceDocument[]): CategoryItem[] {
  const provinceMap = new Map<string, CategoryItem>();

  documents.forEach((doc) => {
    doc.provinceFile.forEach((pf) => {
      const province = pf.province;
      
      if (!provinceMap.has(province)) {
        provinceMap.set(province, {
          id: province,
          title: formatProvinceTitle(province),
          description: `Resource documents for ${formatProvinceTitle(province).toLowerCase()}`,
          thumbnail: pf.thumbnail?.url,
          count: 0,
        });
      }
      
      const category = provinceMap.get(province)!;
      category.count = (category.count || 0) + 1;
    });
  });

  return Array.from(provinceMap.values()).sort((a, b) => {
    // Sort "All Provinces" first, then alphabetically
    if (a.id === "allProvinces") return -1;
    if (b.id === "allProvinces") return 1;
    return a.title.localeCompare(b.title);
  });
}

// Export convenience functions
export const getResources = ResourcesApiClient.getResources;
export const getResourceDocument = ResourcesApiClient.getResourceDocument;
export const getResourcesByProvince = ResourcesApiClient.getResourcesByProvince;
