"use client";

import { useState, useEffect } from "react";
import { ResourceDocument, getResourceDocument } from "../lib/api-client";

export interface UseResourceDocumentResult {
  document?: ResourceDocument;
  isLoading: boolean;
  error: string | null;
  retry: () => void;
}

export function useResourceDocument(documentId: string): UseResourceDocumentResult {
  const [document, setDocument] = useState<ResourceDocument | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDocument = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      if (!documentId || documentId === 'undefined') {
        setError("Invalid document ID");
        setDocument(undefined);
        return;
      }
      
      const response = await getResourceDocument(documentId);
      
      if (response.error) {
        setError(response.error);
        setDocument(undefined);
      } else {
        setDocument(response.data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch resource document");
      setDocument(undefined);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (documentId) {
      fetchDocument();
    }
  }, [documentId]);

  const retry = () => {
    fetchDocument();
  };

  return {
    document,
    isLoading,
    error,
    retry,
  };
}
