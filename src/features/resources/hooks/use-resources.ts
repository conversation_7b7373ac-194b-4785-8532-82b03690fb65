"use client";

import { useState, useEffect } from "react";
import { CategoryItem } from "@/shared/components/category-landing-page";
import { ResourceDocument, getResources, transformDocumentsToCategories } from "../lib/api-client";

export interface UseResourcesResult {
  categories: CategoryItem[];
  documents: ResourceDocument[];
  isLoading: boolean;
  error: string | null;
  retry: () => void;
}

export function useResources(): UseResourcesResult {
  const [documents, setDocuments] = useState<ResourceDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchResources = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await getResources();
      
      if (response.error) {
        setError(response.error);
        setDocuments([]);
      } else {
        setDocuments(response.data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch resources");
      setDocuments([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchResources();
  }, []);

  const retry = () => {
    fetchResources();
  };

  // Transform documents to categories
  const categories = transformDocumentsToCategories(documents);

  return {
    categories,
    documents,
    isLoading,
    error,
    retry,
  };
}
