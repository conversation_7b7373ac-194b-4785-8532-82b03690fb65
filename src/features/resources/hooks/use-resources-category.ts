"use client";

import { useState, useEffect } from "react";
import { ResourceDocument, getResourcesByProvince } from "../lib/api-client";

export interface UseResourcesCategoryResult {
  items: ResourceDocument[];
  isLoading: boolean;
  error: string | null;
  retry: () => void;
}

export function useResourcesCategory(category: string): UseResourcesCategoryResult {
  const [items, setItems] = useState<ResourceDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchResourcesByCategory = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await getResourcesByProvince(category);
      
      if (response.error) {
        setError(response.error);
        setItems([]);
      } else {
        setItems(response.data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch resources for this category");
      setItems([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (category) {
      fetchResourcesByCategory();
    }
  }, [category]);

  const retry = () => {
    fetchResourcesByCategory();
  };

  return {
    items,
    isLoading,
    error,
    retry,
  };
}
